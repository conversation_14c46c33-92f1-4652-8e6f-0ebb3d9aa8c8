import pygame
import random
import sys

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
GRID_SIZE = 30
GRID_WIDTH = 10
GRID_HEIGHT = 20
SIDEBAR_WIDTH = 200

# Calculate the actual play area position to center it
PLAY_AREA_X = (SCREEN_WIDTH - SIDEBAR_WIDTH - GRID_WIDTH * GRID_SIZE) // 2
PLAY_AREA_Y = (SCREEN_HEIGHT - GRID_HEIGHT * GRID_SIZE) // 2

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (128, 128, 128)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)

# Tetromino shapes and colors
SHAPES = [
    # I shape
    [
        ['.....',
         '.....',
         'OOOO.',
         '.....',
         '.....'],
        ['.....',
         '..O..',
         '..O..',
         '..O..',
         '..O..']
    ],
    # J shape
    [
        ['.....',
         '.....',
         '.OOO.',
         '...O.',
         '.....'],
        ['.....',
         '..O..',
         '..O..',
         '.OO..',
         '.....'],
        ['.....',
         '.O...',
         '.OOO.',
         '.....',
         '.....'],
        ['.....',
         '..OO.',
         '..O..',
         '..O..',
         '.....']
    ],
    # L shape
    [
        ['.....',
         '.....',
         '.OOO.',
         '.O...',
         '.....'],
        ['.....',
         '.OO..',
         '..O..',
         '..O..',
         '.....'],
        ['.....',
         '...O.',
         '.OOO.',
         '.....',
         '.....'],
        ['.....',
         '..O..',
         '..O..',
         '..OO.',
         '.....']
    ],
    # O shape
    [
        ['.....',
         '.....',
         '.OO..',
         '.OO..',
         '.....']
    ],
    # S shape
    [
        ['.....',
         '.....',
         '..OO.',
         '.OO..',
         '.....'],
        ['.....',
         '.O...',
         '.OO..',
         '..O..',
         '.....']
    ],
    # T shape
    [
        ['.....',
         '.....',
         '.OOO.',
         '..O..',
         '.....'],
        ['.....',
         '..O..',
         '.OO..',
         '..O..',
         '.....'],
        ['.....',
         '..O..',
         '.OOO.',
         '.....',
         '.....'],
        ['.....',
         '..O..',
         '..OO.',
         '..O..',
         '.....']
    ],
    # Z shape
    [
        ['.....',
         '.....',
         '.OO..',
         '..OO.',
         '.....'],
        ['.....',
         '..O..',
         '.OO..',
         '.O...',
         '.....']
    ]
]

SHAPE_COLORS = [
    CYAN,    # I shape
    BLUE,    # J shape
    ORANGE,  # L shape
    YELLOW,  # O shape
    GREEN,   # S shape
    PURPLE,  # T shape
    RED      # Z shape
]

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Tetris")
clock = pygame.time.Clock()

# Font setup
font_large = pygame.font.SysFont(None, 48)
font_medium = pygame.font.SysFont(None, 36)
font_small = pygame.font.SysFont(None, 24)

class Tetromino:
    def __init__(self, x, y, shape_index):
        self.x = x
        self.y = y
        self.shape_index = shape_index
        self.color = SHAPE_COLORS[shape_index]
        self.rotation = 0
        self.shape = SHAPES[shape_index][self.rotation]
    
    def rotate(self, grid):
        # Save the current rotation
        old_rotation = self.rotation
        
        # Try to rotate
        self.rotation = (self.rotation + 1) % len(SHAPES[self.shape_index])
        self.shape = SHAPES[self.shape_index][self.rotation]
        
        # Check if the rotation is valid
        if self.collision(grid):
            # If not, revert back
            self.rotation = old_rotation
            self.shape = SHAPES[self.shape_index][self.rotation]
            return False
        return True
    
    def move(self, dx, dy, grid):
        # Save current position
        old_x, old_y = self.x, self.y
        
        # Try to move
        self.x += dx
        self.y += dy
        
        # Check if the move is valid
        if self.collision(grid):
            # If not, revert back
            self.x, self.y = old_x, old_y
            return False
        return True
    
    def collision(self, grid):
        # Check each cell of the tetromino
        for y, row in enumerate(self.shape):
            for x, cell in enumerate(row):
                if cell == 'O':
                    # Calculate the position on the grid
                    grid_x = self.x + x
                    grid_y = self.y + y
                    
                    # Check if out of bounds
                    if (grid_x < 0 or grid_x >= GRID_WIDTH or 
                        grid_y >= GRID_HEIGHT or 
                        (grid_y >= 0 and grid[grid_y][grid_x] != BLACK)):
                        return True
        return False
    
    def lock(self, grid):
        # Add the tetromino to the grid
        for y, row in enumerate(self.shape):
            for x, cell in enumerate(row):
                if cell == 'O':
                    # Calculate the position on the grid
                    grid_x = self.x + x
                    grid_y = self.y + y
                    
                    # Only add to grid if within bounds
                    if 0 <= grid_y < GRID_HEIGHT and 0 <= grid_x < GRID_WIDTH:
                        grid[grid_y][grid_x] = self.color
        return grid
    
    def draw(self, surface):
        # Draw each cell of the tetromino
        for y, row in enumerate(self.shape):
            for x, cell in enumerate(row):
                if cell == 'O':
                    # Calculate the position on the screen
                    rect_x = PLAY_AREA_X + (self.x + x) * GRID_SIZE
                    rect_y = PLAY_AREA_Y + (self.y + y) * GRID_SIZE
                    
                    # Draw the cell
                    pygame.draw.rect(surface, self.color, (rect_x, rect_y, GRID_SIZE, GRID_SIZE))
                    pygame.draw.rect(surface, WHITE, (rect_x, rect_y, GRID_SIZE, GRID_SIZE), 1)

class Game:
    def __init__(self):
        # Create an empty grid
        self.grid = [[BLACK for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = self.new_piece()
        self.next_piece = self.new_piece()
        self.game_over = False
        self.paused = False
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.fall_speed = 0.5  # Seconds between automatic drops
        self.fall_time = 0
        self.high_score = 0
    
    def new_piece(self):
        # Create a new random tetromino
        shape_index = random.randint(0, len(SHAPES) - 1)
        return Tetromino(GRID_WIDTH // 2 - 2, -2, shape_index)
    
    def check_lines(self):
        # Check for completed lines
        lines_to_clear = []
        for y, row in enumerate(self.grid):
            if all(cell != BLACK for cell in row):
                lines_to_clear.append(y)
        
        # Clear the lines and add score
        if lines_to_clear:
            # Update score based on number of lines cleared at once
            if len(lines_to_clear) == 1:
                self.score += 100 * self.level
            elif len(lines_to_clear) == 2:
                self.score += 300 * self.level
            elif len(lines_to_clear) == 3:
                self.score += 500 * self.level
            elif len(lines_to_clear) == 4:
                self.score += 800 * self.level  # Tetris!
            
            # Update lines cleared and check for level up
            self.lines_cleared += len(lines_to_clear)
            if self.lines_cleared >= self.level * 10:
                self.level_up()
            
            # Remove the lines
            for line in sorted(lines_to_clear):
                del self.grid[line]
                self.grid.insert(0, [BLACK for _ in range(GRID_WIDTH)])
    
    def level_up(self):
        self.level += 1
        self.fall_speed = max(0.05, 0.5 - (self.level - 1) * 0.05)  # Speed up as level increases
    
    def update(self, delta_time):
        if self.game_over or self.paused:
            return
        
        # Update fall time
        self.fall_time += delta_time
        
        # Move piece down automatically after fall_speed seconds
        if self.fall_time >= self.fall_speed:
            self.fall_time = 0
            if not self.current_piece.move(0, 1, self.grid):
                # If can't move down, lock the piece and create a new one
                self.grid = self.current_piece.lock(self.grid)
                self.check_lines()
                self.current_piece = self.next_piece
                self.next_piece = self.new_piece()
                
                # Check for game over
                if self.current_piece.collision(self.grid):
                    self.game_over = True
                    if self.score > self.high_score:
                        self.high_score = self.score
    
    def handle_input(self, event):
        if self.game_over:
            if event.type == pygame.KEYDOWN and event.key == pygame.K_r:
                self.__init__()  # Reset game
            return
        
        if self.paused:
            if event.type == pygame.KEYDOWN and event.key == pygame.K_p:
                self.paused = False
            return
        
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_LEFT:
                self.current_piece.move(-1, 0, self.grid)
            elif event.key == pygame.K_RIGHT:
                self.current_piece.move(1, 0, self.grid)
            elif event.key == pygame.K_DOWN:
                self.current_piece.move(0, 1, self.grid)
            elif event.key == pygame.K_UP:
                self.current_piece.rotate(self.grid)
            elif event.key == pygame.K_SPACE:
                # Hard drop
                while self.current_piece.move(0, 1, self.grid):
                    pass
                self.grid = self.current_piece.lock(self.grid)
                self.check_lines()
                self.current_piece = self.next_piece
                self.next_piece = self.new_piece()
                
                # Check for game over
                if self.current_piece.collision(self.grid):
                    self.game_over = True
                    if self.score > self.high_score:
                        self.high_score = self.score
            elif event.key == pygame.K_p:
                self.paused = True
    
    def draw_grid(self, surface):
        # Draw the background grid
        for y in range(GRID_HEIGHT):
            for x in range(GRID_WIDTH):
                rect_x = PLAY_AREA_X + x * GRID_SIZE
                rect_y = PLAY_AREA_Y + y * GRID_SIZE
                
                # Draw the cell
                pygame.draw.rect(surface, self.grid[y][x], (rect_x, rect_y, GRID_SIZE, GRID_SIZE))
                pygame.draw.rect(surface, GRAY, (rect_x, rect_y, GRID_SIZE, GRID_SIZE), 1)
    
    def draw_sidebar(self, surface):
        # Draw the sidebar background
        sidebar_x = SCREEN_WIDTH - SIDEBAR_WIDTH
        pygame.draw.rect(surface, BLACK, (sidebar_x, 0, SIDEBAR_WIDTH, SCREEN_HEIGHT))
        pygame.draw.line(surface, WHITE, (sidebar_x, 0), (sidebar_x, SCREEN_HEIGHT), 2)
        
        # Draw the next piece preview
        next_text = font_medium.render("Next Piece:", True, WHITE)
        surface.blit(next_text, (sidebar_x + 20, 30))
        
        # Draw the next piece
        next_piece_x = sidebar_x + SIDEBAR_WIDTH // 2 - 2 * GRID_SIZE
        next_piece_y = 100
        
        for y, row in enumerate(self.next_piece.shape):
            for x, cell in enumerate(row):
                if cell == 'O':
                    rect_x = next_piece_x + x * GRID_SIZE
                    rect_y = next_piece_y + y * GRID_SIZE
                    pygame.draw.rect(surface, self.next_piece.color, (rect_x, rect_y, GRID_SIZE, GRID_SIZE))
                    pygame.draw.rect(surface, WHITE, (rect_x, rect_y, GRID_SIZE, GRID_SIZE), 1)
        
        # Draw the score
        score_text = font_medium.render("Score:", True, WHITE)
        score_value = font_medium.render(str(self.score), True, WHITE)
        surface.blit(score_text, (sidebar_x + 20, 220))
        surface.blit(score_value, (sidebar_x + 20, 260))
        
        # Draw the level
        level_text = font_medium.render("Level:", True, WHITE)
        level_value = font_medium.render(str(self.level), True, WHITE)
        surface.blit(level_text, (sidebar_x + 20, 320))
        surface.blit(level_value, (sidebar_x + 20, 360))
        
        # Draw the lines cleared
        lines_text = font_medium.render("Lines:", True, WHITE)
        lines_value = font_medium.render(str(self.lines_cleared), True, WHITE)
        surface.blit(lines_text, (sidebar_x + 20, 420))
        surface.blit(lines_value, (sidebar_x + 20, 460))
        
        # Draw high score
        high_score_text = font_small.render("High Score:", True, WHITE)
        high_score_value = font_small.render(str(self.high_score), True, WHITE)
        surface.blit(high_score_text, (sidebar_x + 20, 520))
        surface.blit(high_score_value, (sidebar_x + 20, 550))
    
    def draw_play_area(self, surface):
        # Draw the play area border
        pygame.draw.rect(surface, WHITE, (
            PLAY_AREA_X - 2, 
            PLAY_AREA_Y - 2, 
            GRID_WIDTH * GRID_SIZE + 4, 
            GRID_HEIGHT * GRID_SIZE + 4
        ), 2)
    
    def draw(self, surface):
        # Clear the screen
        surface.fill(BLACK)
        
        # Draw the play area
        self.draw_play_area(surface)
        
        # Draw the grid
        self.draw_grid(surface)
        
        # Draw the current piece
        self.current_piece.draw(surface)
        
        # Draw the sidebar
        self.draw_sidebar(surface)
        
        # Draw game over or paused message
        if self.game_over:
            # Draw semi-transparent overlay
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))
            surface.blit(overlay, (0, 0))
            
            game_over_text = font_large.render("GAME OVER", True, RED)
            restart_text = font_medium.render("Press R to Restart", True, WHITE)
            
            surface.blit(game_over_text, (
                SCREEN_WIDTH // 2 - game_over_text.get_width() // 2,
                SCREEN_HEIGHT // 2 - game_over_text.get_height() // 2 - 30
            ))
            surface.blit(restart_text, (
                SCREEN_WIDTH // 2 - restart_text.get_width() // 2,
                SCREEN_HEIGHT // 2 + 30
            ))
        elif self.paused:
            # Draw semi-transparent overlay
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))
            surface.blit(overlay, (0, 0))
            
            paused_text = font_large.render("PAUSED", True, YELLOW)
            continue_text = font_medium.render("Press P to Continue", True, WHITE)
            
            surface.blit(paused_text, (
                SCREEN_WIDTH // 2 - paused_text.get_width() // 2,
                SCREEN_HEIGHT // 2 - paused_text.get_height() // 2 - 30
            ))
            surface.blit(continue_text, (
                SCREEN_WIDTH // 2 - continue_text.get_width() // 2,
                SCREEN_HEIGHT // 2 + 30
            ))

def draw_title_screen(surface):
    surface.fill(BLACK)
    
    title_text = font_large.render("TETRIS", True, WHITE)
    start_text = font_medium.render("Press ENTER to Start", True, WHITE)
    controls_text1 = font_small.render("Controls:", True, WHITE)
    controls_text2 = font_small.render("Arrow Keys - Move", True, WHITE)
    controls_text3 = font_small.render("Up Arrow - Rotate", True, WHITE)
    controls_text4 = font_small.render("Space - Hard Drop", True, WHITE)
    controls_text5 = font_small.render("P - Pause", True, WHITE)
    
    surface.blit(title_text, (SCREEN_WIDTH // 2 - title_text.get_width() // 2, 150))
    surface.blit(start_text, (SCREEN_WIDTH // 2 - start_text.get_width() // 2, 250))
    surface.blit(controls_text1, (SCREEN_WIDTH // 2 - controls_text1.get_width() // 2, 350))
    surface.blit(controls_text2, (SCREEN_WIDTH // 2 - controls_text2.get_width() // 2, 380))
    surface.blit(controls_text3, (SCREEN_WIDTH // 2 - controls_text3.get_width() // 2, 410))
    surface.blit(controls_text4, (SCREEN_WIDTH // 2 - controls_text4.get_width() // 2, 440))
    surface.blit(controls_text5, (SCREEN_WIDTH // 2 - controls_text5.get_width() // 2, 470))
    
    # Draw a simple tetromino animation
    current_time = pygame.time.get_ticks() // 1000
    for i, color in enumerate(SHAPE_COLORS):
        offset = (current_time + i) % 7
        x = SCREEN_WIDTH // 2 - 150 + offset * 50
        y = 50
        pygame.draw.rect(surface, color, (x, y, 30, 30))
        pygame.draw.rect(surface, WHITE, (x, y, 30, 30), 1)

def main():
    game = Game()
    in_title_screen = True
    
    while True:
        delta_time = clock.tick(60) / 1000.0  # Convert to seconds
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            
            if in_title_screen:
                if event.type == pygame.KEYDOWN and event.key == pygame.K_RETURN:
                    in_title_screen = False
            else:
                game.handle_input(event)
        
        # Update game state
        if not in_title_screen:
            game.update(delta_time)
        
        # Draw everything
        if in_title_screen:
            draw_title_screen(screen)
        else:
            game.draw(screen)
        
        # Update the display
        pygame.display.flip()

if __name__ == "__main__":
    main()
