# Space Shooter Game

A classic space shooter game created with Pygame, featuring programmatically generated graphics.

## Game Description

Control your spaceship, shoot down enemy ships, collect power-ups, and survive as long as possible while earning points. The game gets progressively harder as you advance through levels.

## Controls

- **Arrow Keys** or **WASD**: Move your spaceship
- **Space**: Fire lasers
- **R**: Restart the game after game over

## How to Play

1. Run the game with `python space_shooter.py`
2. Use arrow keys or WASD to move your spaceship
3. Press Space to shoot lasers at enemy ships
4. Collect power-ups to enhance your ship:
   - Yellow power-up: Increases your weapon power (up to level 3)
   - White cross: Restores health
   - Blue shield: Adds shield protection
5. Avoid enemy ships and their lasers
6. Defeat enemies to earn points and advance levels
7. If you lose all lives, the game is over
8. Press R to restart after game over

## Game Features

- Player spaceship with health and shield systems
- Multiple enemy types with different behaviors
- Boss enemies that appear periodically
- Three weapon power levels
- Various power-ups (shield, health, weapon upgrade)
- Increasing difficulty with level progression
- Score tracking and lives system
- Parallax star background
- Explosions and visual effects

## Requirements

- Python 3.x
- Pygame library

## Installation

If you don't have Pygame installed, you can install it with:

```
pip install pygame
```

## Technical Details

All game graphics are generated programmatically using Pygame's drawing functions - no external image files are used. This makes the game completely self-contained in a single Python file.
