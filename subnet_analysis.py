from pyspark.sql import SparkSession
from pyspark.sql.functions import col, udf, count, when
from pyspark.sql.types import StringType, IntegerType, StructType, StructField
import requests
import os
import re
import ipaddress

# Initialize Spark Session
spark = SparkSession.builder \
    .appName("Subnet Analysis") \
    .enableHiveSupport() \
    .getOrCreate()

# Function to download country subnet files from GitHub
def download_country_subnet_files(base_url, output_dir):
    """
    Download subnet files for each country from GitHub repository
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Get the list of country files from the GitHub repository
    repo_url = "https://api.github.com/repos/oneoy/iplist/contents/data/country"
    response = requests.get(repo_url)
    
    if response.status_code != 200:
        print(f"Failed to get repository contents: {response.status_code}")
        return []
    
    country_files = []
    for item in response.json():
        if item["type"] == "file" and item["name"].endswith(".txt"):
            country_code = item["name"].split(".")[0]
            download_url = item["download_url"]
            
            # Download the file
            file_response = requests.get(download_url)
            if file_response.status_code == 200:
                file_path = os.path.join(output_dir, item["name"])
                with open(file_path, "wb") as f:
                    f.write(file_response.content)
                country_files.append((country_code, file_path))
                print(f"Downloaded {item['name']}")
            else:
                print(f"Failed to download {item['name']}: {file_response.status_code}")
    
    return country_files

# Function to parse subnet files
def parse_subnet_files(country_files):
    """
    Parse subnet files and extract subnet, mask, and country code
    """
    subnet_data = []
    
    for country_code, file_path in country_files:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    # Extract subnet and mask
                    match = re.match(r"(\d+\.\d+\.\d+\.\d+)/(\d+)", line)
                    if match:
                        subnet, mask = match.groups()
                        subnet_data.append((subnet, mask, country_code))
    
    return subnet_data

# UDF to determine subnet class
def determine_subnet_class(subnet):
    """
    Determine if a subnet is Class A, B, or C
    Class A: ******* to *************** (First octet: 1-126)
    Class B: ********* to *************** (First octet: 128-191)
    Class C: ********* to *************** (First octet: 192-223)
    """
    try:
        first_octet = int(subnet.split('.')[0])
        if 1 <= first_octet <= 126:
            return "A"
        elif 128 <= first_octet <= 191:
            return "B"
        elif 192 <= first_octet <= 223:
            return "C"
        else:
            return "Other"
    except:
        return "Invalid"

# Main function
def main():
    # Download country subnet files
    output_dir = "country_subnets"
    country_files = download_country_subnet_files("https://github.com/oneoy/iplist/tree/master/data/country", output_dir)
    
    # Parse subnet files
    subnet_data = parse_subnet_files(country_files)
    
    # Create schema for DataFrame
    schema = StructType([
        StructField("subnet", StringType(), False),
        StructField("mask", StringType(), False),
        StructField("country_code", StringType(), False)
    ])
    
    # Create DataFrame
    subnet_df = spark.createDataFrame(subnet_data, schema)
    
    # Register UDF
    subnet_class_udf = udf(determine_subnet_class, StringType())
    
    # Add subnet class column
    subnet_df = subnet_df.withColumn("subnet_class", subnet_class_udf(col("subnet")))
    
    # Count subnets by country and class
    result_df = subnet_df.groupBy("country_code") \
        .agg(
            count(when(col("subnet_class") == "A", 1)).alias("class_a_count"),
            count(when(col("subnet_class") == "B", 1)).alias("class_b_count"),
            count(when(col("subnet_class") == "C", 1)).alias("class_c_count"),
            count(when(col("subnet_class") == "Other", 1)).alias("other_count")
        )
    
    # Show results
    result_df.show()
    
    # Create Hive table and save results
    result_df.write.mode("overwrite").saveAsTable("subnet_counts_by_country")
    
    print("Results saved to Hive table: subnet_counts_by_country")
    
    # Clean up
    spark.stop()

if __name__ == "__main__":
    main()
