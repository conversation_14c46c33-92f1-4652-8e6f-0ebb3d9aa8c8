import pygame
import sys
import random
import math

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
TILE_SIZE = 40
GRID_WIDTH = SCREEN_WIDTH // TILE_SIZE
GRID_HEIGHT = SCREEN_HEIGHT // TILE_SIZE

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
DARK_GREEN = (0, 100, 0)
BROWN = (139, 69, 19)
LIGHT_BLUE = (173, 216, 230)

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Tank Battle")
clock = pygame.time.Clock()

# Font setup
font = pygame.font.SysFont(None, 36)

# Generate game surfaces
def create_tank_surface(color, size=30):
    """Create a tank surface with the given color"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    
    # Tank body
    pygame.draw.rect(surface, color, (size//4, size//4, size//2, size//2))
    
    # Tank turret
    pygame.draw.circle(surface, color, (size//2, size//2), size//4)
    
    # Tank cannon
    pygame.draw.rect(surface, color, (size//2, size//2 - size//10, size//2, size//5))
    
    return surface

def create_bullet_surface(color, size=8):
    """Create a bullet surface"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    pygame.draw.circle(surface, color, (size//2, size//2), size//2)
    return surface

def create_explosion_surface(size=40):
    """Create an explosion surface"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    
    # Draw explosion rays
    for i in range(0, 360, 45):
        x = size//2 + (size//2 - 5) * math.cos(math.radians(i))
        y = size//2 + (size//2 - 5) * math.sin(math.radians(i))
        pygame.draw.line(surface, YELLOW, (size//2, size//2), (x, y), 3)
    
    # Center
    pygame.draw.circle(surface, RED, (size//2, size//2), size//4)
    
    return surface

def create_wall_surface(size=TILE_SIZE):
    """Create a brick wall surface"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    
    # Fill with brick color
    pygame.draw.rect(surface, BROWN, (0, 0, size, size))
    
    # Draw brick pattern
    brick_width = size // 4
    brick_height = size // 8
    
    for y in range(0, size, brick_height * 2):
        # First row of bricks
        for x in range(0, size, brick_width * 2):
            pygame.draw.rect(surface, BLACK, (x, y, brick_width, brick_height), 1)
            pygame.draw.rect(surface, BLACK, (x + brick_width, y, brick_width, brick_height), 1)
        
        # Second row of bricks (offset)
        if y + brick_height < size:
            for x in range(0, size, brick_width * 2):
                pygame.draw.rect(surface, BLACK, (x - brick_width//2, y + brick_height, brick_width, brick_height), 1)
                pygame.draw.rect(surface, BLACK, (x + brick_width//2, y + brick_height, brick_width, brick_height), 1)
    
    return surface

def create_steel_surface(size=TILE_SIZE):
    """Create a steel wall surface"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    
    # Fill with steel color
    pygame.draw.rect(surface, GRAY, (0, 0, size, size))
    
    # Draw steel pattern
    pygame.draw.line(surface, BLACK, (0, 0), (size, size), 2)
    pygame.draw.line(surface, BLACK, (0, size), (size, 0), 2)
    pygame.draw.rect(surface, BLACK, (0, 0, size, size), 2)
    
    return surface

def create_water_surface(size=TILE_SIZE):
    """Create a water surface"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    
    # Fill with water color
    pygame.draw.rect(surface, LIGHT_BLUE, (0, 0, size, size))
    
    # Draw wave pattern
    wave_height = size // 8
    for y in range(0, size, wave_height * 2):
        pygame.draw.rect(surface, BLUE, (0, y, size, wave_height // 2), 0)
    
    return surface

def create_grass_surface(size=TILE_SIZE):
    """Create a grass surface"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    
    # Fill with grass color
    pygame.draw.rect(surface, GREEN, (0, 0, size, size))
    
    # Add some texture
    for i in range(0, size, 5):
        for j in range(0, size, 5):
            if random.random() < 0.3:
                pygame.draw.rect(surface, DARK_GREEN, (i, j, 2, 2))
    
    return surface

def create_base_surface(size=TILE_SIZE):
    """Create a base (flag) surface"""
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    
    # Base background
    pygame.draw.rect(surface, GRAY, (0, 0, size, size))
    
    # Flag
    flag_pole_x = size // 2
    pygame.draw.rect(surface, BLACK, (flag_pole_x, size//4, size//10, size//2))
    pygame.draw.polygon(surface, RED, [
        (flag_pole_x + size//10, size//4),
        (flag_pole_x + size//2, size//3),
        (flag_pole_x + size//10, size//2)
    ])
    
    # Star
    pygame.draw.circle(surface, YELLOW, (flag_pole_x + size//4, size//3), size//10)
    
    return surface

# Create all game surfaces
surfaces = {
    'player_tank': create_tank_surface(GREEN),
    'enemy_tank': create_tank_surface(RED),
    'bullet': create_bullet_surface(BLACK),
    'explosion': create_explosion_surface(),
    'brick_wall': create_wall_surface(),
    'steel_wall': create_steel_surface(),
    'water': create_water_surface(),
    'grass': create_grass_surface(),
    'base': create_base_surface()
}

class Tank:
    def __init__(self, x, y, direction, speed, is_player=False):
        self.x = x
        self.y = y
        self.direction = direction  # 0: up, 1: right, 2: down, 3: left
        self.speed = speed
        self.is_player = is_player
        self.image = surfaces['player_tank'] if is_player else surfaces['enemy_tank']
        self.rect = pygame.Rect(x, y, 30, 30)
        self.shoot_cooldown = 0
        self.shoot_delay = 30  # Frames between shots
        self.alive = True
    
    def update(self, walls):
        if not self.alive:
            return
            
        # Decrease shoot cooldown
        if self.shoot_cooldown > 0:
            self.shoot_cooldown -= 1
        
        # Store old position for collision detection
        old_x, old_y = self.x, self.y
        
        # Move based on direction
        if self.direction == 0:  # Up
            self.y -= self.speed
        elif self.direction == 1:  # Right
            self.x += self.speed
        elif self.direction == 2:  # Down
            self.y += self.speed
        elif self.direction == 3:  # Left
            self.x -= self.speed
            
        # Update rectangle position
        self.rect.x = self.x
        self.rect.y = self.y
        
        # Check screen boundaries
        if self.x < 0:
            self.x = 0
            self.rect.x = self.x
        elif self.x > SCREEN_WIDTH - 30:
            self.x = SCREEN_WIDTH - 30
            self.rect.x = self.x
            
        if self.y < 0:
            self.y = 0
            self.rect.y = self.y
        elif self.y > SCREEN_HEIGHT - 30:
            self.y = SCREEN_HEIGHT - 30
            self.rect.y = self.y
            
        # Check wall collisions
        for wall in walls:
            if self.rect.colliderect(wall.rect):
                # Revert to old position
                self.x, self.y = old_x, old_y
                self.rect.x = self.x
                self.rect.y = self.y
                break
    
    def shoot(self):
        if not self.alive or self.shoot_cooldown > 0:
            return None
            
        self.shoot_cooldown = self.shoot_delay
        
        # Calculate bullet starting position based on tank direction
        if self.direction == 0:  # Up
            bullet_x = self.x + 11
            bullet_y = self.y - 8
            bullet_dir = 0
        elif self.direction == 1:  # Right
            bullet_x = self.x + 30
            bullet_y = self.y + 11
            bullet_dir = 1
        elif self.direction == 2:  # Down
            bullet_x = self.x + 11
            bullet_y = self.y + 30
            bullet_dir = 2
        else:  # Left
            bullet_x = self.x - 8
            bullet_y = self.y + 11
            bullet_dir = 3
            
        return Bullet(bullet_x, bullet_y, bullet_dir, self.is_player)
    
    def draw(self):
        if not self.alive:
            return
            
        # Rotate the tank image based on direction
        rotated_image = pygame.transform.rotate(self.image, -90 * self.direction)
        screen.blit(rotated_image, (self.x, self.y))

class Bullet:
    def __init__(self, x, y, direction, is_player_bullet):
        self.x = x
        self.y = y
        self.direction = direction
        self.speed = 5
        self.is_player_bullet = is_player_bullet
        self.image = surfaces['bullet']
        self.rect = pygame.Rect(x, y, 8, 8)
    
    def update(self):
        # Move based on direction
        if self.direction == 0:  # Up
            self.y -= self.speed
        elif self.direction == 1:  # Right
            self.x += self.speed
        elif self.direction == 2:  # Down
            self.y += self.speed
        elif self.direction == 3:  # Left
            self.x -= self.speed
            
        # Update rectangle position
        self.rect.x = self.x
        self.rect.y = self.y
        
        # Check if bullet is off-screen
        if (self.x < 0 or self.x > SCREEN_WIDTH or 
            self.y < 0 or self.y > SCREEN_HEIGHT):
            return True
            
        return False
    
    def draw(self):
        screen.blit(self.image, (self.x, self.y))

class Wall:
    def __init__(self, x, y, wall_type):
        self.x = x
        self.y = y
        self.type = wall_type
        
        if wall_type == 'brick':
            self.image = surfaces['brick_wall']
            self.destructible = True
        elif wall_type == 'steel':
            self.image = surfaces['steel_wall']
            self.destructible = False
        elif wall_type == 'water':
            self.image = surfaces['water']
            self.destructible = False
        elif wall_type == 'grass':
            self.image = surfaces['grass']
            self.destructible = False
        elif wall_type == 'base':
            self.image = surfaces['base']
            self.destructible = True
            
        self.rect = pygame.Rect(x, y, TILE_SIZE, TILE_SIZE)
    
    def draw(self):
        screen.blit(self.image, (self.x, self.y))

class Explosion:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.image = surfaces['explosion']
        self.frame = 0
        self.max_frame = 20  # How long the explosion lasts
    
    def update(self):
        self.frame += 1
        if self.frame >= self.max_frame:
            return True
        return False
    
    def draw(self):
        # Make explosion fade out
        alpha = 255 * (1 - self.frame / self.max_frame)
        temp_image = self.image.copy()
        temp_image.set_alpha(alpha)
        screen.blit(temp_image, (self.x, self.y))

class Game:
    def __init__(self):
        self.player = Tank(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50, 0, 2, True)
        self.enemies = []
        self.bullets = []
        self.walls = []
        self.explosions = []
        self.base = None
        
        self.enemy_spawn_timer = 0
        self.enemy_spawn_rate = 180  # Frames between enemy spawns
        self.max_enemies = 5
        
        self.score = 0
        self.level = 1
        self.game_over = False
        self.victory = False
        
        self.create_level()
    
    def create_level(self):
        # Clear existing objects
        self.walls = []
        
        # Create border walls
        for x in range(0, SCREEN_WIDTH, TILE_SIZE):
            self.walls.append(Wall(x, 0, 'steel'))
            self.walls.append(Wall(x, SCREEN_HEIGHT - TILE_SIZE, 'steel'))
            
        for y in range(TILE_SIZE, SCREEN_HEIGHT - TILE_SIZE, TILE_SIZE):
            self.walls.append(Wall(0, y, 'steel'))
            self.walls.append(Wall(SCREEN_WIDTH - TILE_SIZE, y, 'steel'))
        
        # Create some random walls
        for _ in range(30):
            x = random.randint(1, GRID_WIDTH - 2) * TILE_SIZE
            y = random.randint(1, GRID_HEIGHT - 3) * TILE_SIZE
            
            # Don't place walls too close to player starting position
            if abs(x - self.player.x) < TILE_SIZE * 2 and abs(y - self.player.y) < TILE_SIZE * 2:
                continue
                
            wall_type = random.choice(['brick', 'brick', 'brick', 'steel', 'water', 'grass'])
            self.walls.append(Wall(x, y, wall_type))
        
        # Create base (flag)
        base_x = SCREEN_WIDTH // 2 - TILE_SIZE // 2
        base_y = SCREEN_HEIGHT - TILE_SIZE * 2
        self.base = Wall(base_x, base_y, 'base')
        self.walls.append(self.base)
        
        # Create protective walls around base
        for dx, dy in [(0, -1), (1, 0), (-1, 0), (0, 1)]:
            self.walls.append(Wall(base_x + dx * TILE_SIZE, base_y + dy * TILE_SIZE, 'brick'))
    
    def spawn_enemy(self):
        # Choose a random position at the top of the screen
        x = random.randint(1, GRID_WIDTH - 2) * TILE_SIZE
        y = TILE_SIZE
        
        # Make sure the position is not blocked by a wall
        for wall in self.walls:
            if abs(x - wall.x) < TILE_SIZE and abs(y - wall.y) < TILE_SIZE:
                return
                
        # Create the enemy tank
        self.enemies.append(Tank(x, y, 2, 1))
    
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
                
            if self.game_over or self.victory:
                if event.type == pygame.KEYDOWN and event.key == pygame.K_r:
                    self.__init__()  # Reset game
                continue
                
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    bullet = self.player.shoot()
                    if bullet:
                        self.bullets.append(bullet)
    
    def update(self):
        if self.game_over or self.victory:
            return
            
        # Update player
        keys = pygame.key.get_pressed()
        old_direction = self.player.direction
        
        if keys[pygame.K_UP] or keys[pygame.K_w]:
            self.player.direction = 0
        elif keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.player.direction = 1
        elif keys[pygame.K_DOWN] or keys[pygame.K_s]:
            self.player.direction = 2
        elif keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.player.direction = 3
            
        # Only move if a direction key is pressed
        if (keys[pygame.K_UP] or keys[pygame.K_w] or
            keys[pygame.K_RIGHT] or keys[pygame.K_d] or
            keys[pygame.K_DOWN] or keys[pygame.K_s] or
            keys[pygame.K_LEFT] or keys[pygame.K_a]):
            self.player.update(self.walls)
            
        # Spawn enemies
        self.enemy_spawn_timer += 1
        if (self.enemy_spawn_timer >= self.enemy_spawn_rate and 
            len(self.enemies) < self.max_enemies):
            self.enemy_spawn_timer = 0
            self.spawn_enemy()
            
        # Update enemies
        for enemy in self.enemies:
            # Randomly change direction occasionally
            if random.random() < 0.01:
                enemy.direction = random.randint(0, 3)
                
            # Try to move towards the base or player
            if random.random() < 0.1:
                target = self.base if random.random() < 0.7 else self.player
                
                dx = target.x - enemy.x
                dy = target.y - enemy.y
                
                if abs(dx) > abs(dy):
                    enemy.direction = 1 if dx > 0 else 3
                else:
                    enemy.direction = 2 if dy > 0 else 0
            
            enemy.update(self.walls)
            
            # Randomly shoot
            if random.random() < 0.01:
                bullet = enemy.shoot()
                if bullet:
                    self.bullets.append(bullet)
                    
        # Update bullets
        for bullet in self.bullets[:]:
            if bullet.update():
                self.bullets.remove(bullet)
                continue
                
            # Check collision with tanks
            if bullet.is_player_bullet:
                for enemy in self.enemies[:]:
                    if bullet.rect.colliderect(enemy.rect):
                        enemy.alive = False
                        self.enemies.remove(enemy)
                        self.explosions.append(Explosion(enemy.x - 5, enemy.y - 5))
                        self.score += 100
                        
                        if bullet in self.bullets:
                            self.bullets.remove(bullet)
                        break
            else:
                if bullet.rect.colliderect(self.player.rect):
                    self.player.alive = False
                    self.explosions.append(Explosion(self.player.x - 5, self.player.y - 5))
                    self.game_over = True
                    
                    if bullet in self.bullets:
                        self.bullets.remove(bullet)
                    break
                    
            # Check collision with walls
            for wall in self.walls[:]:
                if bullet.rect.colliderect(wall.rect):
                    # Only destroy destructible walls
                    if wall.destructible:
                        self.walls.remove(wall)
                        self.explosions.append(Explosion(wall.x, wall.y))
                        
                        # Check if base was destroyed
                        if wall == self.base:
                            self.game_over = True
                            
                    if bullet in self.bullets:
                        self.bullets.remove(bullet)
                    break
                    
        # Update explosions
        for explosion in self.explosions[:]:
            if explosion.update():
                self.explosions.remove(explosion)
                
        # Check for victory condition (all enemies defeated)
        if len(self.enemies) == 0 and self.enemy_spawn_timer == 0:
            self.level += 1
            self.max_enemies = min(10, 5 + self.level)
            self.enemy_spawn_rate = max(60, 180 - self.level * 20)
            self.create_level()
            
            if self.level > 5:
                self.victory = True
    
    def draw(self):
        # Clear screen
        screen.fill(BLACK)
        
        # Draw walls
        for wall in self.walls:
            wall.draw()
            
        # Draw player
        self.player.draw()
        
        # Draw enemies
        for enemy in self.enemies:
            enemy.draw()
            
        # Draw bullets
        for bullet in self.bullets:
            bullet.draw()
            
        # Draw explosions
        for explosion in self.explosions:
            explosion.draw()
            
        # Draw UI
        score_text = font.render(f"Score: {self.score}", True, WHITE)
        level_text = font.render(f"Level: {self.level}", True, WHITE)
        enemies_text = font.render(f"Enemies: {len(self.enemies)}/{self.max_enemies}", True, WHITE)
        
        screen.blit(score_text, (10, 10))
        screen.blit(level_text, (10, 50))
        screen.blit(enemies_text, (10, 90))
        
        # Draw game over or victory screen
        if self.game_over:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))
            screen.blit(overlay, (0, 0))
            
            game_over_text = font.render("GAME OVER", True, RED)
            score_text = font.render(f"Final Score: {self.score}", True, WHITE)
            restart_text = font.render("Press R to Restart", True, WHITE)
            
            screen.blit(game_over_text, (SCREEN_WIDTH // 2 - game_over_text.get_width() // 2, 
                                        SCREEN_HEIGHT // 2 - 50))
            screen.blit(score_text, (SCREEN_WIDTH // 2 - score_text.get_width() // 2, 
                                    SCREEN_HEIGHT // 2))
            screen.blit(restart_text, (SCREEN_WIDTH // 2 - restart_text.get_width() // 2, 
                                      SCREEN_HEIGHT // 2 + 50))
        elif self.victory:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))
            screen.blit(overlay, (0, 0))
            
            victory_text = font.render("VICTORY!", True, GREEN)
            score_text = font.render(f"Final Score: {self.score}", True, WHITE)
            restart_text = font.render("Press R to Restart", True, WHITE)
            
            screen.blit(victory_text, (SCREEN_WIDTH // 2 - victory_text.get_width() // 2, 
                                      SCREEN_HEIGHT // 2 - 50))
            screen.blit(score_text, (SCREEN_WIDTH // 2 - score_text.get_width() // 2, 
                                    SCREEN_HEIGHT // 2))
            screen.blit(restart_text, (SCREEN_WIDTH // 2 - restart_text.get_width() // 2, 
                                      SCREEN_HEIGHT // 2 + 50))
        
        # Update display
        pygame.display.flip()
    
    def run(self):
        while True:
            self.handle_events()
            self.update()
            self.draw()
            clock.tick(60)

# Start the game
if __name__ == "__main__":
    game = Game()
    game.run()
