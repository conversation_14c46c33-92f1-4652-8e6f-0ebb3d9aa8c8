# 坦克大战 (Tank Battle)

一个使用 Pygame 创建的简化版坦克大战游戏，所有图形都是程序生成的，无需下载任何外部资源。

## 游戏描述

控制你的坦克保卫基地，消灭敌方坦克。游戏包含多个关卡，每个关卡的敌人数量和生成速度都会增加。保护你的基地旗帜不被敌人摧毁！

## 控制方式

- **方向键** 或 **WASD**: 控制坦克移动和转向
- **空格键**: 发射子弹
- **R**: 游戏结束或胜利后重新开始

## 游戏元素

### 坦克
- **玩家坦克 (绿色)**: 由玩家控制
- **敌方坦克 (红色)**: 自动移动并攻击玩家和基地

### 地形
- **砖墙 (棕色)**: 可被子弹摧毁
- **钢墙 (灰色)**: 不可摧毁
- **水域 (蓝色)**: 坦克无法通过
- **草地 (绿色)**: 坦克可以通过
- **基地 (旗帜)**: 需要保护的目标，被摧毁则游戏结束

## 游戏规则

1. 控制坦克移动并射击敌人
2. 保护基地不被敌人摧毁
3. 消灭所有敌人后进入下一关
4. 完成5个关卡获得胜利
5. 如果玩家坦克被摧毁或基地被摧毁，游戏结束

## 如何开始

运行以下命令启动游戏：

```
python tank_battle.py
```

## 游戏特点

- 程序生成的图形，无需外部资源
- 多种地形元素
- 敌人AI会追踪玩家和基地
- 关卡进度系统
- 爆炸特效
- 计分系统

## 技术细节

- 使用 Pygame 开发
- 所有游戏图形都是通过 Pygame 的绘图函数生成的
- 游戏使用基于网格的碰撞检测系统
- 敌人使用简单的AI逻辑追踪目标
