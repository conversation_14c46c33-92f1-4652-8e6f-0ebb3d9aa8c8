import pygame
import sys
import random
import os
from pygame.locals import *

# Initialize pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60
GRID_SIZE = 80
GRID_WIDTH = 9
GRID_HEIGHT = 5
GRID_START_X = 100
GRID_START_Y = 100
SUN_SPAWN_RATE = 300  # Frames between sun spawns
ZOMBIE_SPAWN_RATE = 500  # Initial frames between zombie spawns
SUN_VALUE = 25
PLANT_COSTS = {
    'sunflower': 50,
    'peashooter': 100,
    'wallnut': 50
}

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GREEN = (0, 200, 0)
YELLOW = (255, 255, 0)
GRAY = (200, 200, 200)

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Plants vs. Zombies Clone")
clock = pygame.time.Clock()

# Load images
def load_image(name):
    path = os.path.join('assets', 'images', name)
    return pygame.image.load(path).convert_alpha()

# Load all images
images = {
    'grass': load_image('grass.png'),
    'sunflower': load_image('sunflower.png'),
    'peashooter': load_image('peashooter.png'),
    'wallnut': load_image('wallnut.png'),
    'zombie': load_image('zombie.png'),
    'sun': load_image('sun.png'),
    'pea': load_image('pea.png'),
    'card': load_image('card.png'),
    'lawnmower': load_image('lawnmower.png')
}

# Font setup
font = pygame.font.SysFont(None, 36)

class Sun:
    def __init__(self, x, y, is_falling=True):
        self.x = x
        self.y = y
        self.is_falling = is_falling
        self.speed = 1 if is_falling else 0
        self.collected = False
        self.image = images['sun']
        self.rect = pygame.Rect(x, y, 30, 30)
        
    def update(self):
        if self.is_falling and not self.collected:
            self.y += self.speed
            self.rect.y = self.y
            
    def draw(self):
        if not self.collected:
            screen.blit(self.image, (self.x, self.y))
            
    def collect(self):
        self.collected = True
        return SUN_VALUE

class Plant:
    def __init__(self, x, y, plant_type):
        self.x = x
        self.y = y
        self.type = plant_type
        self.image = images[plant_type]
        self.health = 100
        self.rect = pygame.Rect(x, y, 60, 60)
        self.attack_cooldown = 0
        self.sun_cooldown = 0 if plant_type == 'sunflower' else None
        
    def update(self, zombies):
        if self.health <= 0:
            return True  # Plant is dead
            
        if self.type == 'peashooter':
            # Attack logic for peashooter
            self.attack_cooldown -= 1
            if self.attack_cooldown <= 0:
                # Check if there are zombies in this row
                for zombie in zombies:
                    if zombie.y == self.y and zombie.x > self.x:
                        return self.shoot()
        
        elif self.type == 'sunflower':
            # Sun production logic
            self.sun_cooldown -= 1
            if self.sun_cooldown <= 0:
                self.sun_cooldown = 300  # Reset cooldown (5 seconds at 60 FPS)
                return self.produce_sun()
                
        return None
        
    def shoot(self):
        self.attack_cooldown = 90  # Reset cooldown (1.5 seconds at 60 FPS)
        return Projectile(self.x + 60, self.y + 30, 'pea')
        
    def produce_sun(self):
        return Sun(self.x + 15, self.y, False)
        
    def take_damage(self, damage):
        self.health -= damage
        
    def draw(self):
        screen.blit(self.image, (self.x, self.y))

class Projectile:
    def __init__(self, x, y, projectile_type):
        self.x = x
        self.y = y
        self.type = projectile_type
        self.image = images[projectile_type]
        self.speed = 5
        self.damage = 20
        self.rect = pygame.Rect(x, y, 20, 20)
        
    def update(self):
        self.x += self.speed
        self.rect.x = self.x
        # Remove if off screen
        if self.x > SCREEN_WIDTH:
            return True
        return False
        
    def draw(self):
        screen.blit(self.image, (self.x, self.y))

class Zombie:
    def __init__(self, row):
        self.x = SCREEN_WIDTH
        self.y = GRID_START_Y + row * GRID_SIZE
        self.row = row
        self.image = images['zombie']
        self.speed = 0.5
        self.health = 100
        self.damage = 0.5
        self.rect = pygame.Rect(self.x, self.y, 60, 60)
        self.attack_cooldown = 0
        
    def update(self, plants):
        # Move zombie
        attacking = False
        
        # Check for collision with plants
        for plant in plants:
            if plant.rect.colliderect(self.rect):
                attacking = True
                self.attack_cooldown -= 1
                if self.attack_cooldown <= 0:
                    plant.take_damage(self.damage)
                    self.attack_cooldown = 60  # Reset cooldown (1 second at 60 FPS)
                break
                
        if not attacking:
            self.x -= self.speed
            self.rect.x = self.x
            
        # Check if zombie reached the house
        if self.x <= GRID_START_X - 60:
            return "game_over"
            
        return None
        
    def take_damage(self, damage):
        self.health -= damage
        
    def draw(self):
        screen.blit(self.image, (self.x, self.y))

class LawnMower:
    def __init__(self, row):
        self.x = GRID_START_X - 80
        self.y = GRID_START_Y + row * GRID_SIZE
        self.row = row
        self.image = images['lawnmower']
        self.speed = 0
        self.active = False
        self.used = False
        self.rect = pygame.Rect(self.x, self.y, 60, 60)
        
    def update(self, zombies):
        if self.used:
            return []
            
        # Check if any zombie reached the lawn mower
        if not self.active:
            for zombie in zombies:
                if zombie.row == self.row and zombie.rect.colliderect(self.rect):
                    self.active = True
                    self.speed = 5
                    break
        
        # Move if active
        if self.active:
            self.x += self.speed
            self.rect.x = self.x
            
            # Remove zombies in path
            zombies_to_remove = []
            for i, zombie in enumerate(zombies):
                if zombie.row == self.row and zombie.rect.colliderect(self.rect):
                    zombies_to_remove.append(i)
                    
            # Check if off screen
            if self.x > SCREEN_WIDTH:
                self.used = True
                
            return zombies_to_remove
            
        return []
        
    def draw(self):
        if not self.used:
            screen.blit(self.image, (self.x, self.y))

class Game:
    def __init__(self):
        self.plants = []
        self.zombies = []
        self.projectiles = []
        self.suns = []
        self.lawn_mowers = [LawnMower(row) for row in range(GRID_HEIGHT)]
        self.sun_count = 100  # Starting sun amount
        self.selected_plant = None
        self.sun_spawn_timer = 0
        self.zombie_spawn_timer = 0
        self.zombie_spawn_rate = ZOMBIE_SPAWN_RATE
        self.game_over = False
        self.victory = False
        self.wave = 1
        self.zombies_to_spawn = 5  # Zombies in first wave
        self.zombies_spawned = 0
        
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == QUIT:
                pygame.quit()
                sys.exit()
                
            if self.game_over or self.victory:
                if event.type == KEYDOWN and event.key == K_r:
                    self.__init__()  # Reset game
                continue
                
            if event.type == MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()
                
                # Check if clicked on a sun
                for sun in self.suns:
                    if not sun.collected and sun.rect.collidepoint(mouse_pos):
                        self.sun_count += sun.collect()
                        
                # Check if clicked on plant selection
                if mouse_pos[1] < 90:  # Plant selection area
                    if 10 <= mouse_pos[0] < 80 and self.sun_count >= PLANT_COSTS['sunflower']:
                        self.selected_plant = 'sunflower'
                    elif 90 <= mouse_pos[0] < 160 and self.sun_count >= PLANT_COSTS['peashooter']:
                        self.selected_plant = 'peashooter'
                    elif 170 <= mouse_pos[0] < 240 and self.sun_count >= PLANT_COSTS['wallnut']:
                        self.selected_plant = 'wallnut'
                        
                # Check if clicked on grid to place plant
                elif self.selected_plant and GRID_START_X <= mouse_pos[0] < GRID_START_X + GRID_WIDTH * GRID_SIZE and \
                     GRID_START_Y <= mouse_pos[1] < GRID_START_Y + GRID_HEIGHT * GRID_SIZE:
                    # Calculate grid position
                    grid_x = (mouse_pos[0] - GRID_START_X) // GRID_SIZE
                    grid_y = (mouse_pos[1] - GRID_START_Y) // GRID_SIZE
                    
                    # Check if cell is empty
                    cell_empty = True
                    for plant in self.plants:
                        plant_grid_x = (plant.x - GRID_START_X) // GRID_SIZE
                        plant_grid_y = (plant.y - GRID_START_Y) // GRID_SIZE
                        if grid_x == plant_grid_x and grid_y == plant_grid_y:
                            cell_empty = False
                            break
                            
                    if cell_empty:
                        # Place plant
                        x = GRID_START_X + grid_x * GRID_SIZE + 10  # Center in cell
                        y = GRID_START_Y + grid_y * GRID_SIZE + 10  # Center in cell
                        self.plants.append(Plant(x, y, self.selected_plant))
                        self.sun_count -= PLANT_COSTS[self.selected_plant]
                        self.selected_plant = None
                        
    def update(self):
        if self.game_over or self.victory:
            return
            
        # Spawn sun from sky
        self.sun_spawn_timer += 1
        if self.sun_spawn_timer >= SUN_SPAWN_RATE:
            self.sun_spawn_timer = 0
            x = random.randint(GRID_START_X, GRID_START_X + GRID_WIDTH * GRID_SIZE - 30)
            self.suns.append(Sun(x, 0))
            
        # Spawn zombies
        if self.zombies_spawned < self.zombies_to_spawn:
            self.zombie_spawn_timer += 1
            if self.zombie_spawn_timer >= self.zombie_spawn_rate:
                self.zombie_spawn_timer = 0
                row = random.randint(0, GRID_HEIGHT - 1)
                self.zombies.append(Zombie(row))
                self.zombies_spawned += 1
                
        # Check for wave completion
        if self.zombies_spawned >= self.zombies_to_spawn and len(self.zombies) == 0:
            if self.wave == 3:  # Win after 3 waves
                self.victory = True
            else:
                self.wave += 1
                self.zombies_to_spawn = 5 + 5 * self.wave  # More zombies each wave
                self.zombies_spawned = 0
                self.zombie_spawn_rate = max(100, ZOMBIE_SPAWN_RATE - 100 * self.wave)  # Faster spawns each wave
            
        # Update plants
        for i, plant in enumerate(self.plants):
            result = plant.update(self.zombies)
            if result:
                if isinstance(result, Projectile):
                    self.projectiles.append(result)
                elif isinstance(result, Sun):
                    self.suns.append(result)
                elif result is True:  # Plant is dead
                    self.plants.pop(i)
                    
        # Update projectiles
        for i, projectile in enumerate(self.projectiles[:]):
            if projectile.update():
                self.projectiles.remove(projectile)
                continue
                
            # Check for collision with zombies
            for zombie in self.zombies:
                if projectile.rect.colliderect(zombie.rect):
                    zombie.take_damage(projectile.damage)
                    if projectile in self.projectiles:  # Check if projectile still exists
                        self.projectiles.remove(projectile)
                    break
                    
        # Update zombies
        for i, zombie in enumerate(self.zombies[:]):
            result = zombie.update(self.plants)
            if result == "game_over":
                self.game_over = True
                
            # Check if zombie is dead
            if zombie.health <= 0:
                self.zombies.remove(zombie)
                
        # Update lawn mowers
        for lawn_mower in self.lawn_mowers:
            zombies_to_remove = lawn_mower.update(self.zombies)
            for index in sorted(zombies_to_remove, reverse=True):
                if index < len(self.zombies):
                    self.zombies.pop(index)
                    
        # Update suns
        for sun in self.suns[:]:
            sun.update()
            if sun.collected:
                self.suns.remove(sun)
            elif sun.y > SCREEN_HEIGHT:
                self.suns.remove(sun)
                
    def draw(self):
        # Clear screen
        screen.fill(GREEN)
        
        # Draw grid
        for x in range(GRID_WIDTH):
            for y in range(GRID_HEIGHT):
                screen.blit(images['grass'], 
                           (GRID_START_X + x * GRID_SIZE, 
                            GRID_START_Y + y * GRID_SIZE))
                
        # Draw lawn mowers
        for lawn_mower in self.lawn_mowers:
            lawn_mower.draw()
            
        # Draw plants
        for plant in self.plants:
            plant.draw()
            
        # Draw zombies
        for zombie in self.zombies:
            zombie.draw()
            
        # Draw projectiles
        for projectile in self.projectiles:
            projectile.draw()
            
        # Draw suns
        for sun in self.suns:
            sun.draw()
            
        # Draw UI
        # Sun counter
        pygame.draw.circle(screen, YELLOW, (30, 30), 20)
        sun_text = font.render(str(self.sun_count), True, BLACK)
        screen.blit(sun_text, (60, 20))
        
        # Plant selection cards
        # Sunflower
        screen.blit(images['card'], (10, 0))
        screen.blit(pygame.transform.scale(images['sunflower'], (50, 50)), (20, 10))
        cost_text = font.render(str(PLANT_COSTS['sunflower']), True, BLACK)
        screen.blit(cost_text, (30, 60))
        
        # Peashooter
        screen.blit(images['card'], (90, 0))
        screen.blit(pygame.transform.scale(images['peashooter'], (50, 50)), (100, 10))
        cost_text = font.render(str(PLANT_COSTS['peashooter']), True, BLACK)
        screen.blit(cost_text, (110, 60))
        
        # Wall-nut
        screen.blit(images['card'], (170, 0))
        screen.blit(pygame.transform.scale(images['wallnut'], (50, 50)), (180, 10))
        cost_text = font.render(str(PLANT_COSTS['wallnut']), True, BLACK)
        screen.blit(cost_text, (190, 60))
        
        # Selected plant preview
        if self.selected_plant:
            mouse_pos = pygame.mouse.get_pos()
            screen.blit(images[self.selected_plant], (mouse_pos[0] - 30, mouse_pos[1] - 30))
            
        # Wave indicator
        wave_text = font.render(f"Wave {self.wave}/3", True, BLACK)
        screen.blit(wave_text, (SCREEN_WIDTH - 150, 20))
        
        # Game over / victory screen
        if self.game_over:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))
            screen.blit(overlay, (0, 0))
            
            game_over_text = font.render("GAME OVER - Zombies ate your brains!", True, WHITE)
            restart_text = font.render("Press R to restart", True, WHITE)
            
            screen.blit(game_over_text, (SCREEN_WIDTH // 2 - game_over_text.get_width() // 2, 
                                        SCREEN_HEIGHT // 2 - 50))
            screen.blit(restart_text, (SCREEN_WIDTH // 2 - restart_text.get_width() // 2, 
                                      SCREEN_HEIGHT // 2 + 50))
                                      
        elif self.victory:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))
            screen.blit(overlay, (0, 0))
            
            victory_text = font.render("VICTORY - You defeated the zombie horde!", True, WHITE)
            restart_text = font.render("Press R to restart", True, WHITE)
            
            screen.blit(victory_text, (SCREEN_WIDTH // 2 - victory_text.get_width() // 2, 
                                      SCREEN_HEIGHT // 2 - 50))
            screen.blit(restart_text, (SCREEN_WIDTH // 2 - restart_text.get_width() // 2, 
                                     SCREEN_HEIGHT // 2 + 50))
        
        # Update display
        pygame.display.flip()
        
    def run(self):
        while True:
            self.handle_events()
            self.update()
            self.draw()
            clock.tick(FPS)

# Start the game
if __name__ == "__main__":
    game = Game()
    game.run()
