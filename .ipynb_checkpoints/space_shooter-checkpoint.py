import pygame
import sys
import random
import math
from pygame.locals import *

# Initialize pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
CYAN = (0, 255, 255)
PURPLE = (128, 0, 128)

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Space Shooter")
clock = pygame.time.Clock()

# Font setup
font = pygame.font.SysFont(None, 36)

# Generate game surfaces
def create_player_ship_surface():
    surface = pygame.Surface((60, 60), pygame.SRCALPHA)
    # Ship body
    pygame.draw.polygon(surface, BLUE, [(30, 10), (50, 30), (30, 50), (10, 30)])
    # Cockpit
    pygame.draw.circle(surface, CYAN, (30, 30), 10)
    # Engines
    pygame.draw.rect(surface, RED, (10, 25, 5, 10))
    pygame.draw.rect(surface, RED, (45, 25, 5, 10))
    return surface

def create_enemy_ship_surface():
    surface = pygame.Surface((40, 40), pygame.SRCALPHA)
    # Ship body
    pygame.draw.polygon(surface, RED, [(20, 5), (35, 20), (20, 35), (5, 20)])
    # Cockpit
    pygame.draw.circle(surface, YELLOW, (20, 20), 6)
    # Wings
    pygame.draw.rect(surface, PURPLE, (10, 10, 20, 4))
    pygame.draw.rect(surface, PURPLE, (10, 26, 20, 4))
    return surface

def create_boss_ship_surface():
    surface = pygame.Surface((100, 100), pygame.SRCALPHA)
    # Ship body
    pygame.draw.polygon(surface, PURPLE, [(50, 20), (80, 50), (50, 80), (20, 50)])
    # Cockpit
    pygame.draw.circle(surface, RED, (50, 50), 15)
    # Weapons
    pygame.draw.rect(surface, YELLOW, (20, 40, 10, 20))
    pygame.draw.rect(surface, YELLOW, (70, 40, 10, 20))
    # Wings
    pygame.draw.polygon(surface, RED, [(20, 30), (40, 20), (40, 80), (20, 70)])
    pygame.draw.polygon(surface, RED, [(80, 30), (60, 20), (60, 80), (80, 70)])
    return surface

def create_laser_surface(color):
    surface = pygame.Surface((5, 15), pygame.SRCALPHA)
    pygame.draw.rect(surface, color, (0, 0, 5, 15))
    return surface

def create_explosion_surface(size):
    surface = pygame.Surface((size, size), pygame.SRCALPHA)
    # Draw explosion rays
    for i in range(0, 360, 45):
        x = size//2 + (size//2 - 5) * math.cos(math.radians(i))
        y = size//2 + (size//2 - 5) * math.sin(math.radians(i))
        pygame.draw.line(surface, YELLOW, (size//2, size//2), (x, y), 3)
    # Center
    pygame.draw.circle(surface, RED, (size//2, size//2), size//4)
    return surface

def create_star_surface():
    surface = pygame.Surface((3, 3), pygame.SRCALPHA)
    pygame.draw.circle(surface, WHITE, (1, 1), 1)
    return surface

def create_powerup_surface():
    surface = pygame.Surface((30, 30), pygame.SRCALPHA)
    pygame.draw.circle(surface, YELLOW, (15, 15), 15)
    # Draw a "P" in the center
    pygame.draw.rect(surface, BLACK, (10, 5, 3, 20))
    pygame.draw.rect(surface, BLACK, (10, 5, 10, 3))
    pygame.draw.rect(surface, BLACK, (10, 15, 10, 3))
    return surface

def create_health_surface():
    surface = pygame.Surface((30, 30), pygame.SRCALPHA)
    # Red cross on white background
    pygame.draw.circle(surface, WHITE, (15, 15), 15)
    pygame.draw.rect(surface, RED, (7, 13, 16, 4))
    pygame.draw.rect(surface, RED, (13, 7, 4, 16))
    return surface

def create_shield_surface():
    surface = pygame.Surface((60, 60), pygame.SRCALPHA)
    # Blue shield
    pygame.draw.circle(surface, (100, 100, 255, 128), (30, 30), 30, 5)
    return surface

# Create all game surfaces
surfaces = {
    'player_ship': create_player_ship_surface(),
    'enemy_ship': create_enemy_ship_surface(),
    'boss_ship': create_boss_ship_surface(),
    'player_laser': create_laser_surface(GREEN),
    'enemy_laser': create_laser_surface(RED),
    'explosion': create_explosion_surface(60),
    'small_explosion': create_explosion_surface(30),
    'star': create_star_surface(),
    'powerup': create_powerup_surface(),
    'health': create_health_surface(),
    'shield': create_shield_surface()
}

class Player:
    def __init__(self):
        self.image = surfaces['player_ship']
        self.rect = self.image.get_rect()
        self.rect.centerx = SCREEN_WIDTH // 2
        self.rect.bottom = SCREEN_HEIGHT - 10
        self.speed = 5
        self.health = 100
        self.max_health = 100
        self.shield = 0
        self.shield_max = 50
        self.fire_rate = 10  # Frames between shots
        self.fire_cooldown = 0
        self.score = 0
        self.lives = 3
        self.power_level = 1
        self.is_shielded = False
        
    def update(self):
        # Movement
        keys = pygame.key.get_pressed()
        if keys[K_LEFT] or keys[K_a]:
            self.rect.x -= self.speed
        if keys[K_RIGHT] or keys[K_d]:
            self.rect.x += self.speed
        if keys[K_UP] or keys[K_w]:
            self.rect.y -= self.speed
        if keys[K_DOWN] or keys[K_s]:
            self.rect.y += self.speed
            
        # Keep player on screen
        if self.rect.left < 0:
            self.rect.left = 0
        if self.rect.right > SCREEN_WIDTH:
            self.rect.right = SCREEN_WIDTH
        if self.rect.top < 0:
            self.rect.top = 0
        if self.rect.bottom > SCREEN_HEIGHT:
            self.rect.bottom = SCREEN_HEIGHT
            
        # Shooting cooldown
        if self.fire_cooldown > 0:
            self.fire_cooldown -= 1
            
    def shoot(self):
        if self.fire_cooldown <= 0:
            self.fire_cooldown = self.fire_rate
            
            if self.power_level == 1:
                # Single laser
                return [Laser(self.rect.centerx - 2, self.rect.top, -1, 'player_laser')]
            elif self.power_level == 2:
                # Double laser
                return [
                    Laser(self.rect.left + 10, self.rect.top, -1, 'player_laser'),
                    Laser(self.rect.right - 10, self.rect.top, -1, 'player_laser')
                ]
            else:
                # Triple laser
                return [
                    Laser(self.rect.centerx - 2, self.rect.top, -1, 'player_laser'),
                    Laser(self.rect.left + 10, self.rect.top + 5, -1, 'player_laser'),
                    Laser(self.rect.right - 10, self.rect.top + 5, -1, 'player_laser')
                ]
        return []
        
    def take_damage(self, damage):
        if self.shield > 0:
            self.shield -= damage
            if self.shield < 0:
                remaining_damage = -self.shield
                self.shield = 0
                self.health -= remaining_damage
            else:
                # No damage to health if shield absorbed it all
                return False
        else:
            self.health -= damage
            
        if self.health <= 0:
            self.lives -= 1
            if self.lives > 0:
                self.health = self.max_health
                return False
            return True
        return False
        
    def add_shield(self, amount):
        self.shield = min(self.shield_max, self.shield + amount)
        self.is_shielded = True
        
    def power_up(self):
        if self.power_level < 3:
            self.power_level += 1
            
    def heal(self, amount):
        self.health = min(self.max_health, self.health + amount)
        
    def draw(self):
        screen.blit(self.image, self.rect)
        if self.shield > 0:
            screen.blit(surfaces['shield'], (self.rect.centerx - 30, self.rect.centery - 30))
            
        # Draw health bar
        health_width = 60
        health_height = 8
        health_x = self.rect.centerx - health_width // 2
        health_y = self.rect.bottom + 5
        
        # Background (red)
        pygame.draw.rect(screen, RED, (health_x, health_y, health_width, health_height))
        # Foreground (green)
        health_fill = max(0, int(health_width * self.health / self.max_health))
        pygame.draw.rect(screen, GREEN, (health_x, health_y, health_fill, health_height))
        
        # Draw shield bar if player has shield
        if self.shield > 0:
            shield_y = health_y + health_height + 2
            # Background (gray)
            pygame.draw.rect(screen, (100, 100, 100), (health_x, shield_y, health_width, health_height))
            # Foreground (blue)
            shield_fill = max(0, int(health_width * self.shield / self.shield_max))
            pygame.draw.rect(screen, CYAN, (health_x, shield_y, shield_fill, health_height))

class Enemy:
    def __init__(self, x, y, enemy_type='normal'):
        self.type = enemy_type
        
        if enemy_type == 'normal':
            self.image = surfaces['enemy_ship']
            self.rect = self.image.get_rect()
            self.health = 30
            self.speed = random.uniform(1, 3)
            self.score_value = 10
            self.fire_rate = random.randint(60, 120)  # Frames between shots
        elif enemy_type == 'boss':
            self.image = surfaces['boss_ship']
            self.rect = self.image.get_rect()
            self.health = 300
            self.speed = 1
            self.score_value = 100
            self.fire_rate = 30  # Frames between shots
            
        self.rect.x = x
        self.rect.y = y
        self.fire_cooldown = random.randint(0, self.fire_rate)
        self.movement_pattern = random.choice(['straight', 'zigzag', 'sine'])
        self.movement_counter = 0
        
    def update(self):
        # Different movement patterns
        if self.movement_pattern == 'straight':
            self.rect.y += self.speed
        elif self.movement_pattern == 'zigzag':
            self.rect.y += self.speed
            self.movement_counter += 1
            self.rect.x += math.sin(self.movement_counter * 0.1) * 2
        elif self.movement_pattern == 'sine':
            self.rect.y += self.speed
            self.rect.x += math.sin(self.rect.y * 0.01) * 2
            
        # Fire cooldown
        if self.fire_cooldown > 0:
            self.fire_cooldown -= 1
            
        # Check if off screen
        if self.rect.top > SCREEN_HEIGHT:
            return True
        return False
        
    def shoot(self):
        if self.fire_cooldown <= 0:
            self.fire_cooldown = self.fire_rate
            
            if self.type == 'normal':
                return [Laser(self.rect.centerx - 2, self.rect.bottom, 1, 'enemy_laser')]
            elif self.type == 'boss':
                # Boss shoots multiple lasers
                return [
                    Laser(self.rect.centerx - 20, self.rect.bottom, 1, 'enemy_laser'),
                    Laser(self.rect.centerx, self.rect.bottom, 1, 'enemy_laser'),
                    Laser(self.rect.centerx + 20, self.rect.bottom, 1, 'enemy_laser')
                ]
        return []
        
    def take_damage(self, damage):
        self.health -= damage
        return self.health <= 0
        
    def draw(self):
        screen.blit(self.image, self.rect)

class Laser:
    def __init__(self, x, y, direction, laser_type):
        self.image = surfaces[laser_type]
        self.rect = self.image.get_rect()
        self.rect.centerx = x
        self.rect.centery = y
        self.speed = 7 * direction  # Positive for down, negative for up
        self.damage = 10 if laser_type == 'player_laser' else 5
        
    def update(self):
        self.rect.y += self.speed
        
        # Check if off screen
        if self.rect.bottom < 0 or self.rect.top > SCREEN_HEIGHT:
            return True
        return False
        
    def draw(self):
        screen.blit(self.image, self.rect)

class Explosion:
    def __init__(self, x, y, size='large'):
        if size == 'large':
            self.image = surfaces['explosion']
        else:
            self.image = surfaces['small_explosion']
        self.rect = self.image.get_rect()
        self.rect.center = (x, y)
        self.frame = 0
        self.max_frame = 20  # How long the explosion lasts
        
    def update(self):
        self.frame += 1
        if self.frame >= self.max_frame:
            return True
        return False
        
    def draw(self):
        # Make explosion fade out
        alpha = 255 * (1 - self.frame / self.max_frame)
        temp_image = self.image.copy()
        temp_image.set_alpha(alpha)
        screen.blit(temp_image, self.rect)

class PowerUp:
    def __init__(self, x, y, powerup_type):
        self.type = powerup_type
        if powerup_type == 'shield':
            self.image = surfaces['shield']
        elif powerup_type == 'power':
            self.image = surfaces['powerup']
        elif powerup_type == 'health':
            self.image = surfaces['health']
        
        self.rect = self.image.get_rect()
        self.rect.center = (x, y)
        self.speed = 2
        
    def update(self):
        self.rect.y += self.speed
        
        # Check if off screen
        if self.rect.top > SCREEN_HEIGHT:
            return True
        return False
        
    def draw(self):
        screen.blit(self.image, self.rect)

class Star:
    def __init__(self):
        self.image = surfaces['star']
        self.rect = self.image.get_rect()
        self.rect.x = random.randint(0, SCREEN_WIDTH)
        self.rect.y = random.randint(0, SCREEN_HEIGHT)
        self.speed = random.uniform(0.1, 0.5)
        
    def update(self):
        self.rect.y += self.speed
        
        # Reset if off screen
        if self.rect.top > SCREEN_HEIGHT:
            self.rect.x = random.randint(0, SCREEN_WIDTH)
            self.rect.y = 0
            
    def draw(self):
        screen.blit(self.image, self.rect)

class Game:
    def __init__(self):
        self.player = Player()
        self.enemies = []
        self.lasers = []
        self.explosions = []
        self.powerups = []
        self.stars = [Star() for _ in range(100)]  # Background stars
        
        self.enemy_spawn_timer = 0
        self.enemy_spawn_rate = 60  # Frames between enemy spawns
        self.boss_spawn_timer = 0
        self.boss_spawn_rate = 1800  # Frames between boss spawns (30 seconds at 60 FPS)
        self.powerup_spawn_timer = 0
        self.powerup_spawn_rate = 600  # Frames between powerup spawns (10 seconds at 60 FPS)
        
        self.game_over = False
        self.level = 1
        self.enemies_killed = 0
        self.enemies_for_level_up = 20
        
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == QUIT:
                pygame.quit()
                sys.exit()
                
            if self.game_over:
                if event.type == KEYDOWN and event.key == K_r:
                    self.__init__()  # Reset game
                continue
                
            if event.type == KEYDOWN:
                if event.key == K_SPACE:
                    new_lasers = self.player.shoot()
                    self.lasers.extend(new_lasers)
                    
    def spawn_enemy(self):
        x = random.randint(50, SCREEN_WIDTH - 50)
        y = random.randint(-100, -50)
        self.enemies.append(Enemy(x, y))
        
    def spawn_boss(self):
        x = SCREEN_WIDTH // 2 - 50  # Center the boss
        y = -100
        self.enemies.append(Enemy(x, y, 'boss'))
        
    def spawn_powerup(self):
        x = random.randint(50, SCREEN_WIDTH - 50)
        y = random.randint(-100, -50)
        powerup_type = random.choice(['shield', 'power', 'health'])
        self.powerups.append(PowerUp(x, y, powerup_type))
        
    def update(self):
        if self.game_over:
            return
            
        # Update player
        self.player.update()
        
        # Spawn enemies
        self.enemy_spawn_timer += 1
        if self.enemy_spawn_timer >= self.enemy_spawn_rate:
            self.enemy_spawn_timer = 0
            self.spawn_enemy()
            
        # Spawn boss
        self.boss_spawn_timer += 1
        if self.boss_spawn_timer >= self.boss_spawn_rate:
            self.boss_spawn_timer = 0
            self.spawn_boss()
            
        # Spawn powerups
        self.powerup_spawn_timer += 1
        if self.powerup_spawn_timer >= self.powerup_spawn_rate:
            self.powerup_spawn_timer = 0
            self.spawn_powerup()
            
        # Update stars
        for star in self.stars:
            star.update()
            
        # Update enemies
        for enemy in self.enemies[:]:
            if enemy.update():
                self.enemies.remove(enemy)
                continue
                
            # Enemy shooting
            new_lasers = enemy.shoot()
            self.lasers.extend(new_lasers)
            
            # Check collision with player
            if self.player.rect.colliderect(enemy.rect):
                if self.player.take_damage(20):
                    self.game_over = True
                
                # Create explosion
                self.explosions.append(Explosion(enemy.rect.centerx, enemy.rect.centery))
                self.enemies.remove(enemy)
                continue
                
        # Update lasers
        for laser in self.lasers[:]:
            if laser.update():
                self.lasers.remove(laser)
                continue
                
            # Check collision with player (enemy lasers)
            if laser.speed > 0 and self.player.rect.colliderect(laser.rect):
                if self.player.take_damage(laser.damage):
                    self.game_over = True
                self.explosions.append(Explosion(laser.rect.centerx, laser.rect.centery, 'small'))
                self.lasers.remove(laser)
                continue
                
            # Check collision with enemies (player lasers)
            if laser.speed < 0:
                for enemy in self.enemies[:]:
                    if enemy.rect.colliderect(laser.rect):
                        if enemy.take_damage(laser.damage):
                            # Enemy destroyed
                            self.player.score += enemy.score_value
                            self.enemies_killed += 1
                            
                            # Check for level up
                            if self.enemies_killed >= self.enemies_for_level_up:
                                self.level_up()
                                
                            # Create explosion
                            self.explosions.append(Explosion(enemy.rect.centerx, enemy.rect.centery))
                            
                            # Chance to drop powerup
                            if random.random() < 0.1:
                                powerup_type = random.choice(['shield', 'power', 'health'])
                                self.powerups.append(PowerUp(enemy.rect.centerx, enemy.rect.centery, powerup_type))
                                
                            self.enemies.remove(enemy)
                            
                        # Remove laser regardless of whether enemy was destroyed
                        if laser in self.lasers:
                            self.explosions.append(Explosion(laser.rect.centerx, laser.rect.centery, 'small'))
                            self.lasers.remove(laser)
                        break
                        
        # Update explosions
        for explosion in self.explosions[:]:
            if explosion.update():
                self.explosions.remove(explosion)
                
        # Update powerups
        for powerup in self.powerups[:]:
            if powerup.update():
                self.powerups.remove(powerup)
                continue
                
            # Check collision with player
            if self.player.rect.colliderect(powerup.rect):
                if powerup.type == 'shield':
                    self.player.add_shield(50)
                elif powerup.type == 'power':
                    self.player.power_up()
                elif powerup.type == 'health':
                    self.player.heal(25)
                    
                self.powerups.remove(powerup)
                
    def level_up(self):
        self.level += 1
        self.enemies_killed = 0
        self.enemies_for_level_up = 20 + 10 * self.level
        self.enemy_spawn_rate = max(20, self.enemy_spawn_rate - 5)
        self.boss_spawn_rate = max(900, self.boss_spawn_rate - 300)
        
    def draw(self):
        # Clear screen
        screen.fill(BLACK)
        
        # Draw stars
        for star in self.stars:
            star.draw()
            
        # Draw player
        self.player.draw()
        
        # Draw enemies
        for enemy in self.enemies:
            enemy.draw()
            
        # Draw lasers
        for laser in self.lasers:
            laser.draw()
            
        # Draw explosions
        for explosion in self.explosions:
            explosion.draw()
            
        # Draw powerups
        for powerup in self.powerups:
            powerup.draw()
            
        # Draw UI
        # Score
        score_text = font.render(f"Score: {self.player.score}", True, WHITE)
        screen.blit(score_text, (10, 10))
        
        # Lives
        lives_text = font.render(f"Lives: {self.player.lives}", True, WHITE)
        screen.blit(lives_text, (10, 50))
        
        # Level
        level_text = font.render(f"Level: {self.level}", True, WHITE)
        screen.blit(level_text, (SCREEN_WIDTH - 150, 10))
        
        # Power level
        power_text = font.render(f"Power: {self.player.power_level}", True, YELLOW)
        screen.blit(power_text, (SCREEN_WIDTH - 150, 50))
        
        # Game over screen
        if self.game_over:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))
            screen.blit(overlay, (0, 0))
            
            game_over_text = font.render("GAME OVER", True, WHITE)
            score_text = font.render(f"Final Score: {self.player.score}", True, WHITE)
            restart_text = font.render("Press R to restart", True, WHITE)
            
            screen.blit(game_over_text, (SCREEN_WIDTH // 2 - game_over_text.get_width() // 2, 
                                        SCREEN_HEIGHT // 2 - 50))
            screen.blit(score_text, (SCREEN_WIDTH // 2 - score_text.get_width() // 2, 
                                    SCREEN_HEIGHT // 2))
            screen.blit(restart_text, (SCREEN_WIDTH // 2 - restart_text.get_width() // 2, 
                                      SCREEN_HEIGHT // 2 + 50))
        
        # Update display
        pygame.display.flip()
        
    def run(self):
        while True:
            self.handle_events()
            self.update()
            self.draw()
            clock.tick(FPS)

# Start the game
if __name__ == "__main__":
    game = Game()
    game.run()
