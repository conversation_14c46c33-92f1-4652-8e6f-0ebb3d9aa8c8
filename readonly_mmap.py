import mmap

def read_only_mmap(filename: str) -> mmap.mmap:
    """Create a read-only memory map of an existing file."""
    with open(filename, "rb") as f:
        # ACCESS_READ is read-only, ACCESS_WRITE is read-write
        return mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)

def example_readonly():
    with open("data.txt", "wb") as f:
        f.write(b"Test data")
    
    mm = read_only_mmap("data.txt")
    try:
        print(mm[:].decode('utf-8'))  # Read entire file
    finally:
        mm.close()
        os.unlink("data.txt")