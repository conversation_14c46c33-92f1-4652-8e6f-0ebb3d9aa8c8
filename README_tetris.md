# 俄罗斯方块 (Tetris)

一个使用 Pygame 创建的经典俄罗斯方块游戏，所有图形都是程序生成的，无需下载任何外部资源。

## 游戏描述

控制下落的方块，旋转并移动它们以填满水平行。当一行被完全填满时，该行会被消除，你将获得分数。随着游戏的进行，方块下落的速度会越来越快。当方块堆叠到屏幕顶部时，游戏结束。

## 控制方式

- **左右方向键**: 水平移动方块
- **下方向键**: 加速下落
- **上方向键**: 旋转方块
- **空格键**: 硬降（立即将方块降到底部）
- **P 键**: 暂停/继续游戏
- **R 键**: 游戏结束后重新开始
- **回车键**: 从标题屏幕开始游戏

## 游戏元素

### 方块类型
- **I 形方块 (青色)**: 长条形
- **J 形方块 (蓝色)**: L形，向右
- **L 形方块 (橙色)**: L形，向左
- **O 形方块 (黄色)**: 正方形
- **S 形方块 (绿色)**: S形
- **T 形方块 (紫色)**: T形
- **Z 形方块 (红色)**: Z形

### 游戏界面
- **主游戏区**: 方块下落和堆叠的区域
- **侧边栏**: 显示下一个方块、当前分数、等级、已消除行数和最高分

## 游戏规则

1. 方块会从屏幕顶部不断下落
2. 使用方向键移动和旋转方块，使其填满水平行
3. 当一整行被填满时，该行会被消除，并获得分数
4. 消除的行数越多，获得的分数越高：
   - 1行: 100 × 当前等级
   - 2行: 300 × 当前等级
   - 3行: 500 × 当前等级
   - 4行: 800 × 当前等级（俄罗斯方块！）
5. 每消除10行，游戏等级提升，方块下落速度加快
6. 当方块堆叠到屏幕顶部时，游戏结束

## 如何开始

运行以下命令启动游戏：

```
python tetris.py
```

## 游戏特点

- 经典的俄罗斯方块玩法
- 分数系统和等级进度
- 下一个方块预览
- 暂停功能
- 最高分记录
- 标题屏幕和游戏结束画面

## 技术细节

- 使用 Pygame 开发
- 所有游戏图形都是通过 Pygame 的绘图函数生成的
- 使用二维数组表示游戏网格
- 实现了碰撞检测和方块旋转逻辑
