import mmap
import os
from typing import Optional

class MemoryMapDemo:
    def __init__(self, filename: str):
        self.filename = filename
        self.mmap: Optional[mmap.mmap] = None
    
    def create_new_file(self, size: int) -> None:
        """Create a new file with specified size."""
        with open(self.filename, 'wb') as f:
            f.write(b'\x00' * size)  # Initialize with zeros
    
    def write_demo(self) -> None:
        """Demonstrate writing to a memory-mapped file."""
        print("\n=== Write Demo ===")
        # Create a new 1KB file
        self.create_new_file(1024)
        
        # Open file for both reading and writing
        with open(self.filename, 'r+b') as f:
            # Create memory map
            mm = mmap.mmap(f.fileno(), 0)
            
            try:
                # Write some data
                mm.write(b"Hello, Memory Map!")
                
                # Write at specific position
                mm.seek(20)
                mm.write(b"This is at position 20")
                
                # Using slice notation
                mm[50:60] = b"0123456789"
                
                print(f"Wrote data to {self.filename}")
                
            finally:
                mm.close()
    
    def read_demo(self) -> None:
        """Demonstrate reading from a memory-mapped file."""
        print("\n=== Read Demo ===")
        with open(self.filename, 'rb') as f:
            mm = mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)
            
            try:
                # Read first 17 bytes
                mm.seek(0)
                print("First 17 bytes:", mm.read(17).decode('utf-8'))
                
                # Read from position 20
                mm.seek(20)
                print("From position 20:", mm.read(22).decode('utf-8'))
                
                # Read using slice notation
                print("Positions 50-60:", mm[50:60].decode('utf-8'))
                
                # Search for content
                pattern = b"Memory"
                pos = mm.find(pattern)
                if pos != -1:
                    print(f"Found 'Memory' at position: {pos}")
                
            finally:
                mm.close()
    
    def modify_demo(self) -> None:
        """Demonstrate modifying data in-place."""
        print("\n=== Modify Demo ===")
        with open(self.filename, 'r+b') as f:
            mm = mmap.mmap(f.fileno(), 0)
            
            try:
                # Replace "Hello" with "HELLO"
                mm.seek(0)
                current = mm.read(5).decode('utf-8')
                print(f"Before modification: {current}")
                
                mm.seek(0)
                mm.write(b"HELLO")
                
                mm.seek(0)
                modified = mm.read(5).decode('utf-8')
                print(f"After modification: {modified}")
                
            finally:
                mm.close()
    
    def cleanup(self) -> None:
        """Remove the demo file."""
        try:
            os.unlink(self.filename)
            print(f"\nCleaned up {self.filename}")
        except FileNotFoundError:
            pass

def run_demo():
    demo = MemoryMapDemo("mmap_demo.dat")
    try:
        demo.write_demo()
        demo.read_demo()
        demo.modify_demo()
    finally:
        demo.cleanup()

if __name__ == "__main__":
    run_demo()