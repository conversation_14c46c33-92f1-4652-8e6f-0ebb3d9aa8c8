import pygame
import os

# Initialize pygame
pygame.init()

# Create assets directory if it doesn't exist
os.makedirs('assets/images', exist_ok=True)

# Define colors
GREEN = (0, 200, 0)
DARK_GREEN = (0, 150, 0)
LIGHT_GREEN = (100, 255, 100)
BROWN = (139, 69, 19)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
RED = (255, 0, 0)
GREY = (100, 100, 100)
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)

# Function to create and save a surface
def create_image(filename, width, height, draw_function):
    surface = pygame.Surface((width, height), pygame.SRCALPHA)
    draw_function(surface)
    pygame.image.save(surface, os.path.join('assets/images', filename))
    print(f"Created {filename}")

# Background tile
def draw_grass(surface):
    surface.fill(GREEN)
    # Add some texture
    for i in range(0, surface.get_width(), 5):
        for j in range(0, surface.get_height(), 5):
            if (i + j) % 2 == 0:
                pygame.draw.rect(surface, DARK_GREEN, (i, j, 2, 2))
create_image('grass.png', 80, 80, draw_grass)

# Sunflower
def draw_sunflower(surface):
    # Stem
    pygame.draw.rect(surface, DARK_GREEN, (25, 30, 10, 30))
    # Flower
    pygame.draw.circle(surface, YELLOW, (30, 25), 20)
    # Center
    pygame.draw.circle(surface, BROWN, (30, 25), 10)
create_image('sunflower.png', 60, 60, draw_sunflower)

# Peashooter
def draw_peashooter(surface):
    # Stem and base
    pygame.draw.rect(surface, DARK_GREEN, (25, 30, 10, 30))
    # Head
    pygame.draw.circle(surface, GREEN, (30, 25), 15)
    # Shooter
    pygame.draw.ellipse(surface, LIGHT_GREEN, (40, 20, 20, 10))
create_image('peashooter.png', 60, 60, draw_peashooter)

# Wall-nut
def draw_wallnut(surface):
    # Nut
    pygame.draw.ellipse(surface, BROWN, (10, 10, 40, 50))
    # Eyes
    pygame.draw.circle(surface, BLACK, (25, 25), 5)
    pygame.draw.circle(surface, BLACK, (35, 25), 5)
    # Mouth
    pygame.draw.arc(surface, BLACK, (20, 30, 20, 20), 0, 3.14, 2)
create_image('wallnut.png', 60, 60, draw_wallnut)

# Zombie
def draw_zombie(surface):
    # Body
    pygame.draw.rect(surface, GREY, (10, 10, 30, 50))
    # Head
    pygame.draw.circle(surface, GREY, (25, 10), 15)
    # Eyes
    pygame.draw.circle(surface, RED, (20, 5), 5)
    pygame.draw.circle(surface, RED, (30, 5), 5)
    # Arms
    pygame.draw.rect(surface, GREY, (0, 20, 10, 5))
    pygame.draw.rect(surface, GREY, (40, 20, 10, 5))
create_image('zombie.png', 60, 60, draw_zombie)

# Sun
def draw_sun(surface):
    pygame.draw.circle(surface, YELLOW, (15, 15), 15)
    # Rays
    for i in range(0, 360, 45):
        x = 15 + 18 * pygame.math.Vector2(1, 0).rotate(i).x
        y = 15 + 18 * pygame.math.Vector2(1, 0).rotate(i).y
        pygame.draw.line(surface, YELLOW, (15, 15), (x, y), 3)
create_image('sun.png', 30, 30, draw_sun)

# Pea projectile
def draw_pea(surface):
    pygame.draw.circle(surface, LIGHT_GREEN, (10, 10), 8)
create_image('pea.png', 20, 20, draw_pea)

# Card backgrounds for plant selection
def draw_card(surface):
    pygame.draw.rect(surface, WHITE, (0, 0, 70, 90))
    pygame.draw.rect(surface, BLACK, (0, 0, 70, 90), 2)  # Border
create_image('card.png', 70, 90, draw_card)

# Lawn mower
def draw_lawnmower(surface):
    # Base
    pygame.draw.rect(surface, GREY, (10, 30, 40, 20))
    # Wheels
    pygame.draw.circle(surface, BLACK, (15, 50), 10)
    pygame.draw.circle(surface, BLACK, (45, 50), 10)
    # Handle
    pygame.draw.rect(surface, GREY, (30, 10, 5, 20))
    # Blade
    pygame.draw.ellipse(surface, BLUE, (0, 20, 15, 30))
create_image('lawnmower.png', 60, 60, draw_lawnmower)

print("All assets generated successfully!")
