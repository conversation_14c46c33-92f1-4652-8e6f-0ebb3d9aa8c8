import pygame
import random
import sys

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
PLAYER_SIZE = 50
COIN_SIZE = 30
ENEMY_SIZE = 40
PLAYER_SPEED = 5
ENEMY_SPEED = 3
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Pygame Demo Game")
clock = pygame.time.Clock()

# Font setup
font = pygame.font.SysFont(None, 36)

class Player:
    def __init__(self):
        self.rect = pygame.Rect(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2, PLAYER_SIZE, PLAYER_SIZE)
        self.color = BLUE
        self.speed = PLAYER_SPEED
        
    def move(self, dx, dy):
        # Keep player within screen bounds
        if 0 <= self.rect.x + dx <= SCREEN_WIDTH - PLAYER_SIZE:
            self.rect.x += dx
        if 0 <= self.rect.y + dy <= SCREEN_HEIGHT - PLAYER_SIZE:
            self.rect.y += dy
            
    def draw(self):
        pygame.draw.rect(screen, self.color, self.rect)

class Coin:
    def __init__(self):
        self.rect = pygame.Rect(
            random.randint(0, SCREEN_WIDTH - COIN_SIZE),
            random.randint(0, SCREEN_HEIGHT - COIN_SIZE),
            COIN_SIZE, COIN_SIZE
        )
        self.color = YELLOW
        
    def draw(self):
        pygame.draw.ellipse(screen, self.color, self.rect)

class Enemy:
    def __init__(self):
        # Spawn from one of the edges
        side = random.randint(0, 3)
        if side == 0:  # Top
            x = random.randint(0, SCREEN_WIDTH - ENEMY_SIZE)
            y = -ENEMY_SIZE
        elif side == 1:  # Right
            x = SCREEN_WIDTH
            y = random.randint(0, SCREEN_HEIGHT - ENEMY_SIZE)
        elif side == 2:  # Bottom
            x = random.randint(0, SCREEN_WIDTH - ENEMY_SIZE)
            y = SCREEN_HEIGHT
        else:  # Left
            x = -ENEMY_SIZE
            y = random.randint(0, SCREEN_HEIGHT - ENEMY_SIZE)
            
        self.rect = pygame.Rect(x, y, ENEMY_SIZE, ENEMY_SIZE)
        self.color = RED
        self.speed = ENEMY_SPEED
        
    def update(self, player_rect):
        # Move toward player
        if self.rect.x < player_rect.x:
            self.rect.x += self.speed
        elif self.rect.x > player_rect.x:
            self.rect.x -= self.speed
            
        if self.rect.y < player_rect.y:
            self.rect.y += self.speed
        elif self.rect.y > player_rect.y:
            self.rect.y -= self.speed
            
    def draw(self):
        pygame.draw.rect(screen, self.color, self.rect)

def main():
    # Game objects
    player = Player()
    coins = [Coin() for _ in range(5)]
    enemies = [Enemy()]
    
    # Game state
    score = 0
    game_over = False
    enemy_spawn_timer = 0
    
    # Main game loop
    while True:
        # Event handling
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r and game_over:
                    # Restart game
                    return main()
                
        if game_over:
            # Display game over screen
            screen.fill(BLACK)
            game_over_text = font.render(f"Game Over! Final Score: {score}", True, WHITE)
            restart_text = font.render("Press R to Restart", True, WHITE)
            screen.blit(game_over_text, (SCREEN_WIDTH // 2 - game_over_text.get_width() // 2, 
                                         SCREEN_HEIGHT // 2 - game_over_text.get_height() // 2))
            screen.blit(restart_text, (SCREEN_WIDTH // 2 - restart_text.get_width() // 2, 
                                      SCREEN_HEIGHT // 2 + 50))
            pygame.display.flip()
            clock.tick(60)
            continue
            
        # Player movement
        keys = pygame.key.get_pressed()
        dx, dy = 0, 0
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            dx = -player.speed
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            dx = player.speed
        if keys[pygame.K_UP] or keys[pygame.K_w]:
            dy = -player.speed
        if keys[pygame.K_DOWN] or keys[pygame.K_s]:
            dy = player.speed
        player.move(dx, dy)
        
        # Coin collection
        for coin in coins[:]:
            if player.rect.colliderect(coin.rect):
                coins.remove(coin)
                coins.append(Coin())
                score += 10
                
        # Enemy movement and collision
        for enemy in enemies:
            enemy.update(player.rect)
            if player.rect.colliderect(enemy.rect):
                game_over = True
                
        # Spawn new enemies over time
        enemy_spawn_timer += 1
        if enemy_spawn_timer >= 180 and len(enemies) < 5 + score // 50:  # Spawn every 3 seconds, more as score increases
            enemies.append(Enemy())
            enemy_spawn_timer = 0
            
        # Drawing
        screen.fill(BLACK)
        
        # Draw game objects
        for coin in coins:
            coin.draw()
        for enemy in enemies:
            enemy.draw()
        player.draw()
        
        # Draw score
        score_text = font.render(f"Score: {score}", True, WHITE)
        screen.blit(score_text, (10, 10))
        
        # Update display
        pygame.display.flip()
        clock.tick(60)

if __name__ == "__main__":
    main()
