# 子网分析程序

这个项目包含三个 PySpark 脚本，用于分析国家子网信息，并统计每个国家的 A、B、C 类子网地址数量。

## 脚本说明

1. `subnet_analysis.py` - 从 GitHub 下载子网信息文件，解析后进行分析
2. `subnet_analysis_optimized.py` - 直接从 GitHub API 获取子网信息，无需下载文件
3. `subnet_analysis_local.py` - 从本地目录读取子网信息文件进行分析

## 功能

这些脚本执行以下操作：

1. 获取子网信息（从 GitHub 或本地文件）
2. 创建包含子网、掩码和国家代号的 Spark DataFrame
3. 将每个子网分类为 A 类、B 类或 C 类
4. 统计每个国家的各类子网数量
5. 将结果保存到 Hive 表中

## IP 地址分类标准

- **A 类**：******* 到 ***************（第一个八位字节：1-126）
- **B 类**：********* 到 ***************（第一个八位字节：128-191）
- **C 类**：********* 到 ***************（第一个八位字节：192-223）
- **其他**：不属于上述类别的 IP 地址

## 使用方法

### 前提条件

- 安装 Apache Spark
- 配置 Hive 支持
- 安装 Python 依赖：`pip install pyspark requests`

### 运行脚本

#### 方法 1：从 GitHub 下载文件后分析

```bash
spark-submit subnet_analysis.py
```

这将从 GitHub 下载子网文件到 `country_subnets` 目录，然后进行分析。

#### 方法 2：直接从 GitHub API 获取数据分析（推荐）

```bash
spark-submit subnet_analysis_optimized.py
```

这将直接从 GitHub API 获取数据，无需下载文件，更加高效。

#### 方法 3：从本地文件分析

```bash
spark-submit subnet_analysis_local.py
```

这将从本地 `country_subnets` 目录读取子网文件进行分析。如果您已经下载了文件或在无网络环境中工作，可以使用此方法。

### 输出

所有脚本都会：

1. 在控制台显示样本数据和结果
2. 将结果保存到名为 `subnet_counts_by_country` 的 Hive 表中

## Hive 表结构

```sql
CREATE TABLE subnet_counts_by_country (
  country_code STRING,
  class_a_count BIGINT,
  class_b_count BIGINT,
  class_c_count BIGINT,
  other_count BIGINT
)
```

## 查询示例

```sql
-- 查看所有国家的子网统计
SELECT * FROM subnet_counts_by_country;

-- 查找 A 类子网最多的国家
SELECT country_code, class_a_count 
FROM subnet_counts_by_country 
ORDER BY class_a_count DESC 
LIMIT 10;

-- 计算每个国家的总子网数量
SELECT 
  country_code, 
  (class_a_count + class_b_count + class_c_count + other_count) AS total_subnets 
FROM subnet_counts_by_country 
ORDER BY total_subnets DESC;
```

## 注意事项

- 这些脚本需要网络连接才能从 GitHub 获取数据
- 处理大量子网信息可能需要较长时间
- 确保 Spark 和 Hive 环境正确配置
