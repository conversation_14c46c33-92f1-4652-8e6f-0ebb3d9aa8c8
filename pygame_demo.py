import pygame
import sys
import random
import math

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
LIGHT_GRAY = (192, 192, 192)

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Pygame Demo - Interactive Examples")
clock = pygame.time.Clock()

# Font setup
font_large = pygame.font.SysFont(None, 48)
font_medium = pygame.font.SysFont(None, 36)
font_small = pygame.font.SysFont(None, 24)

class Particle:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.vx = random.uniform(-5, 5)
        self.vy = random.uniform(-5, 5)
        self.color = (random.randint(100, 255), random.randint(100, 255), random.randint(100, 255))
        self.size = random.randint(2, 8)
        self.life = 255
        self.decay = random.uniform(2, 5)
    
    def update(self):
        self.x += self.vx
        self.y += self.vy
        self.vy += 0.1  # Gravity
        self.life -= self.decay
        
        # Bounce off walls
        if self.x <= 0 or self.x >= SCREEN_WIDTH:
            self.vx *= -0.8
        if self.y <= 0 or self.y >= SCREEN_HEIGHT:
            self.vy *= -0.8
            
        return self.life > 0
    
    def draw(self, surface):
        if self.life > 0:
            alpha = max(0, min(255, int(self.life)))
            color = (*self.color, alpha)
            # Create a surface with per-pixel alpha
            particle_surface = pygame.Surface((self.size * 2, self.size * 2), pygame.SRCALPHA)
            pygame.draw.circle(particle_surface, color, (self.size, self.size), self.size)
            surface.blit(particle_surface, (self.x - self.size, self.y - self.size))

class BouncingBall:
    def __init__(self, x, y, radius, color):
        self.x = x
        self.y = y
        self.radius = radius
        self.color = color
        self.vx = random.uniform(-8, 8)
        self.vy = random.uniform(-8, 8)
        self.trail = []
    
    def update(self):
        # Update position
        self.x += self.vx
        self.y += self.vy
        
        # Bounce off walls
        if self.x - self.radius <= 0 or self.x + self.radius >= SCREEN_WIDTH:
            self.vx *= -1
        if self.y - self.radius <= 0 or self.y + self.radius >= SCREEN_HEIGHT:
            self.vy *= -1
            
        # Keep within bounds
        self.x = max(self.radius, min(SCREEN_WIDTH - self.radius, self.x))
        self.y = max(self.radius, min(SCREEN_HEIGHT - self.radius, self.y))
        
        # Add to trail
        self.trail.append((self.x, self.y))
        if len(self.trail) > 20:
            self.trail.pop(0)
    
    def draw(self, surface):
        # Draw trail
        for i, pos in enumerate(self.trail):
            alpha = int(255 * (i / len(self.trail)))
            trail_color = (*self.color, alpha)
            trail_surface = pygame.Surface((self.radius * 2, self.radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(trail_surface, trail_color, (self.radius, self.radius), max(1, self.radius - i))
            surface.blit(trail_surface, (pos[0] - self.radius, pos[1] - self.radius))
        
        # Draw ball
        pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), self.radius)
        pygame.draw.circle(surface, WHITE, (int(self.x), int(self.y)), self.radius, 2)

class Firework:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.particles = []
        self.exploded = False
        self.vy = random.uniform(-15, -10)
        self.color = (random.randint(100, 255), random.randint(100, 255), random.randint(100, 255))
        
    def update(self):
        if not self.exploded:
            self.y += self.vy
            self.vy += 0.3  # Gravity
            
            # Explode when reaching peak or random chance
            if self.vy >= 0 or random.random() < 0.02:
                self.explode()
        else:
            # Update particles
            self.particles = [p for p in self.particles if p.update()]
            
        return len(self.particles) > 0 or not self.exploded
    
    def explode(self):
        self.exploded = True
        # Create explosion particles
        for _ in range(random.randint(20, 40)):
            self.particles.append(Particle(self.x, self.y))
    
    def draw(self, surface):
        if not self.exploded:
            # Draw rocket
            pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), 3)
        else:
            # Draw particles
            for particle in self.particles:
                particle.draw(surface)

class DrawingCanvas:
    def __init__(self):
        self.drawing = False
        self.last_pos = None
        self.brush_size = 5
        self.brush_color = RED
        self.canvas = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        self.canvas.fill(WHITE)
    
    def handle_event(self, event):
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left mouse button
                self.drawing = True
                self.last_pos = event.pos
        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1:
                self.drawing = False
                self.last_pos = None
        elif event.type == pygame.MOUSEMOTION:
            if self.drawing and self.last_pos:
                pygame.draw.line(self.canvas, self.brush_color, self.last_pos, event.pos, self.brush_size)
                self.last_pos = event.pos
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_c:
                self.canvas.fill(WHITE)  # Clear canvas
            elif event.key == pygame.K_1:
                self.brush_color = RED
            elif event.key == pygame.K_2:
                self.brush_color = GREEN
            elif event.key == pygame.K_3:
                self.brush_color = BLUE
            elif event.key == pygame.K_4:
                self.brush_color = YELLOW
            elif event.key == pygame.K_5:
                self.brush_color = MAGENTA
            elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                self.brush_size = min(20, self.brush_size + 1)
            elif event.key == pygame.K_MINUS:
                self.brush_size = max(1, self.brush_size - 1)
    
    def draw(self, surface):
        surface.blit(self.canvas, (0, 0))
        
        # Draw brush preview
        mouse_pos = pygame.mouse.get_pos()
        pygame.draw.circle(surface, self.brush_color, mouse_pos, self.brush_size, 2)

class Demo:
    def __init__(self):
        self.mode = "menu"
        self.balls = []
        self.fireworks = []
        self.canvas = DrawingCanvas()
        self.time = 0
        
        # Create some bouncing balls
        for _ in range(5):
            color = (random.randint(100, 255), random.randint(100, 255), random.randint(100, 255))
            ball = BouncingBall(random.randint(50, SCREEN_WIDTH-50), 
                              random.randint(50, SCREEN_HEIGHT-50), 
                              random.randint(10, 30), color)
            self.balls.append(ball)
    
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.mode = "menu"
                elif event.key == pygame.K_1:
                    self.mode = "balls"
                elif event.key == pygame.K_2:
                    self.mode = "fireworks"
                elif event.key == pygame.K_3:
                    self.mode = "drawing"
                elif event.key == pygame.K_4:
                    self.mode = "shapes"
            
            if self.mode == "fireworks" and event.type == pygame.MOUSEBUTTONDOWN:
                self.fireworks.append(Firework(event.pos[0], SCREEN_HEIGHT))
            
            if self.mode == "drawing":
                self.canvas.handle_event(event)
    
    def update(self):
        self.time += 1
        
        if self.mode == "balls":
            for ball in self.balls:
                ball.update()
        
        elif self.mode == "fireworks":
            self.fireworks = [fw for fw in self.fireworks if fw.update()]
            
            # Auto-spawn fireworks occasionally
            if random.random() < 0.02:
                x = random.randint(100, SCREEN_WIDTH - 100)
                self.fireworks.append(Firework(x, SCREEN_HEIGHT))
    
    def draw_menu(self):
        screen.fill(BLACK)
        
        title = font_large.render("Pygame Demo", True, WHITE)
        screen.blit(title, (SCREEN_WIDTH//2 - title.get_width()//2, 100))
        
        options = [
            "Press 1 - Bouncing Balls",
            "Press 2 - Fireworks (Click to launch)",
            "Press 3 - Drawing Canvas",
            "Press 4 - Animated Shapes",
            "Press ESC - Return to menu"
        ]
        
        for i, option in enumerate(options):
            text = font_medium.render(option, True, WHITE)
            screen.blit(text, (SCREEN_WIDTH//2 - text.get_width()//2, 200 + i * 50))
    
    def draw(self):
        if self.mode == "menu":
            self.draw_menu()
        
        elif self.mode == "balls":
            screen.fill(BLACK)
            for ball in self.balls:
                ball.draw(screen)
            
            # Instructions
            text = font_small.render("Bouncing Balls with Trails - Press ESC for menu", True, WHITE)
            screen.blit(text, (10, 10))
        
        elif self.mode == "fireworks":
            screen.fill(BLACK)
            for firework in self.fireworks:
                firework.draw(screen)
            
            # Instructions
            text = font_small.render("Click to launch fireworks - Press ESC for menu", True, WHITE)
            screen.blit(text, (10, 10))
        
        elif self.mode == "drawing":
            self.canvas.draw(screen)
            
            # Instructions
            instructions = [
                "Drawing Canvas - Press ESC for menu",
                "C: Clear, 1-5: Colors, +/-: Brush size"
            ]
            for i, instruction in enumerate(instructions):
                text = font_small.render(instruction, True, BLACK)
                screen.blit(text, (10, 10 + i * 25))
        
        elif self.mode == "shapes":
            screen.fill(BLACK)
            self.draw_animated_shapes()
            
            # Instructions
            text = font_small.render("Animated Shapes - Press ESC for menu", True, WHITE)
            screen.blit(text, (10, 10))
    
    def draw_animated_shapes(self):
        # Rotating squares
        for i in range(5):
            angle = (self.time + i * 20) * 2
            x = SCREEN_WIDTH // 2 + math.cos(math.radians(angle)) * 100
            y = SCREEN_HEIGHT // 2 + math.sin(math.radians(angle)) * 100
            
            # Create rotated square
            size = 30
            points = []
            for j in range(4):
                px = x + math.cos(math.radians(angle * 2 + j * 90)) * size
                py = y + math.sin(math.radians(angle * 2 + j * 90)) * size
                points.append((px, py))
            
            color = (
                int(128 + 127 * math.sin(math.radians(angle))),
                int(128 + 127 * math.sin(math.radians(angle + 120))),
                int(128 + 127 * math.sin(math.radians(angle + 240)))
            )
            pygame.draw.polygon(screen, color, points)
        
        # Pulsing circles
        for i in range(3):
            radius = 20 + 15 * math.sin(math.radians(self.time * 3 + i * 60))
            x = 200 + i * 200
            y = 500
            color = (
                int(128 + 127 * math.sin(math.radians(self.time + i * 120))),
                int(128 + 127 * math.cos(math.radians(self.time + i * 120))),
                255
            )
            pygame.draw.circle(screen, color, (int(x), int(y)), int(radius))
    
    def run(self):
        while True:
            self.handle_events()
            self.update()
            self.draw()
            pygame.display.flip()
            clock.tick(FPS)

# Start the demo
if __name__ == "__main__":
    demo = Demo()
    demo.run()
