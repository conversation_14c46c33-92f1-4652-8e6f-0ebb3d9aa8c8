# Pygame Demo - Interactive Examples

A comprehensive Pygame demonstration showcasing various features and capabilities of the Pygame library.

## Features

This demo includes four interactive examples that demonstrate different aspects of Pygame:

### 1. Bouncing Balls (Press 1)
- Multiple colorful balls bouncing around the screen
- Each ball has a trailing effect showing its path
- Realistic physics with wall bouncing
- Demonstrates: Animation, collision detection, visual effects

### 2. Fireworks (Press 2)
- Interactive firework launcher - click anywhere to launch a firework
- Particle system with realistic physics
- Auto-spawning fireworks for continuous display
- Colorful explosions with fading particles
- Demonstrates: Particle systems, mouse input, advanced graphics

### 3. Drawing Canvas (Press 3)
- Interactive drawing application
- Multiple brush colors and sizes
- Real-time drawing with mouse
- Demonstrates: Mouse input handling, drawing operations, user interaction

#### Drawing Controls:
- **Mouse**: Draw on the canvas
- **C**: Clear the canvas
- **1-5**: Change brush colors (Red, Green, Blue, Yellow, Magenta)
- **+/-**: Increase/decrease brush size

### 4. Animated Shapes (Press 4)
- Rotating squares orbiting around the center
- Pulsing circles with color transitions
- Mathematical animations using sine and cosine functions
- Demonstrates: Mathematical graphics, color manipulation, smooth animations

## Controls

### Global Controls:
- **ESC**: Return to main menu
- **1**: Switch to Bouncing Balls demo
- **2**: Switch to Fireworks demo
- **3**: Switch to Drawing Canvas demo
- **4**: Switch to Animated Shapes demo

### Fireworks Mode:
- **Click**: Launch a firework from the bottom of the screen

### Drawing Mode:
- **Mouse**: Draw on canvas
- **C**: Clear canvas
- **1-5**: Change brush color
- **+/-**: Adjust brush size

## Requirements

- Python 3.x
- Pygame library

## Installation

If you don't have Pygame installed, you can install it with:

```bash
pip install pygame
```

## Running the Demo

```bash
python pygame_demo.py
```

## Technical Features Demonstrated

### Graphics and Rendering
- Basic shape drawing (circles, polygons, lines)
- Alpha blending and transparency effects
- Surface manipulation and blitting
- Color manipulation and gradients

### Animation and Physics
- Smooth movement and rotation
- Collision detection with boundaries
- Particle systems with realistic physics
- Trail effects and visual feedback

### User Input
- Keyboard event handling
- Mouse input processing
- Real-time interaction
- State management

### Game Development Concepts
- Game loop structure
- Frame rate control
- Object-oriented design
- State machines

## Code Structure

The demo is organized into several classes:

- **Particle**: Individual particle with physics and rendering
- **BouncingBall**: Ball object with trail effects and collision
- **Firework**: Firework rocket and explosion system
- **DrawingCanvas**: Interactive drawing surface
- **Demo**: Main application controller

## Educational Value

This demo is perfect for:
- Learning Pygame basics
- Understanding game development concepts
- Exploring graphics programming
- Studying particle systems
- Learning event handling in games

## Possible Extensions

You can extend this demo by adding:
- Sound effects and music
- More particle effects
- Additional drawing tools
- Save/load functionality for drawings
- More complex animations
- Game elements like scoring or objectives
