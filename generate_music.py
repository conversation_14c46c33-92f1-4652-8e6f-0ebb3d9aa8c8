import pygame
import pygame.midi
import os
from midiutil.MidiFile import MIDIFile

# Create the assets/audio directory if it doesn't exist
os.makedirs('assets/audio', exist_ok=True)

def create_background_music():
    # Create a MIDI file with one track
    midi_file = MIDIFile(1)
    
    # Track, channel, time of first note
    track = 0
    channel = 0
    time = 0
    
    # Set track name and tempo
    midi_file.addTrackName(track, time, "Plants vs Zombies Background")
    midi_file.addTempo(track, time, 120)
    
    # Define a simple repeating melody
    # C major scale: C, D, E, F, G, A, B, C
    notes = [60, 62, 64, 65, 67, 69, 71, 72]
    
    # Create a simple melody
    melody = [60, 64, 67, 64, 60, 64, 67, 72, 
              67, 64, 60, 64, 67, 64, 60, 55,
              60, 64, 67, 64, 60, 64, 67, 72,
              67, 64, 60, 55, 60, 64, 67, 72]
    
    # Add the notes to the MIDI file
    duration = 1  # Quarter note
    volume = 100  # 0-127
    
    # Add melody
    for i, pitch in enumerate(melody):
        midi_file.addNote(track, channel, pitch, time + i * duration, duration, volume)
    
    # Add a simple bass line
    bass_notes = [48, 48, 48, 48, 53, 53, 53, 53, 
                 48, 48, 48, 48, 53, 53, 53, 53,
                 48, 48, 48, 48, 53, 53, 53, 53,
                 48, 48, 48, 48, 53, 53, 53, 53]
    
    for i, pitch in enumerate(bass_notes):
        midi_file.addNote(track, channel, pitch, time + i * duration, duration, volume - 20)
    
    # Write the MIDI file
    with open("assets/audio/background_music.mid", "wb") as output_file:
        midi_file.writeFile(output_file)
    
    print("Background music MIDI file created successfully!")

def create_sound_effects():
    # Create a simple "plant placed" sound
    pygame.mixer.init()
    
    # Create a simple beep sound for planting
    sample_rate = 44100
    duration = 0.2  # seconds
    
    # Generate a simple sine wave
    buf = pygame.sndarray.make_sound(
        pygame.sndarray.array([
            32767 * pygame.math.sin(2 * 3.14159 * 440 * t / sample_rate)
            for t in range(int(sample_rate * duration))
        ]).astype('int16')
    )
    
    # Save the sound
    pygame.mixer.Sound.save(buf, "assets/audio/plant.wav")
    print("Plant sound effect created successfully!")
    
    # Create a simple "zombie groan" sound
    duration = 0.5  # seconds
    
    # Generate a lower frequency sound for zombies
    buf = pygame.sndarray.make_sound(
        pygame.sndarray.array([
            16384 * pygame.math.sin(2 * 3.14159 * 150 * t / sample_rate)
            for t in range(int(sample_rate * duration))
        ]).astype('int16')
    )
    
    # Save the sound
    pygame.mixer.Sound.save(buf, "assets/audio/zombie.wav")
    print("Zombie sound effect created successfully!")
    
    # Create a simple "sun collected" sound
    duration = 0.1  # seconds
    
    # Generate a higher frequency sound for sun collection
    buf = pygame.sndarray.make_sound(
        pygame.sndarray.array([
            16384 * pygame.math.sin(2 * 3.14159 * 880 * t / sample_rate)
            for t in range(int(sample_rate * duration))
        ]).astype('int16')
    )
    
    # Save the sound
    pygame.mixer.Sound.save(buf, "assets/audio/sun.wav")
    print("Sun collection sound effect created successfully!")

try:
    # Initialize pygame
    pygame.init()
    
    # Try to create the MIDI file
    create_background_music()
    
    # Try to create sound effects
    create_sound_effects()
    
except ImportError:
    print("Error: MIDIUtil library not found. Installing...")
    import subprocess
    subprocess.call(["pip", "install", "MIDIUtil"])
    print("MIDIUtil installed. Please run the script again.")
except Exception as e:
    print(f"Error creating audio files: {e}")
finally:
    pygame.quit()
