# Plants vs. Zombies Clone

A simplified clone of the popular Plants vs. Zombies game created with Pygame.

## Game Description

Defend your lawn from zombie invaders by strategically placing plants. Collect sun resources to plant more defenders and survive three increasingly difficult waves of zombies.

## Controls

- **Mouse**: Select plants and place them on the grid
- **R**: Restart the game after game over or victory

## How to Play

1. Run the game with `python plants_vs_zombies.py`
2. Collect suns that fall from the sky or are produced by Sunflowers
3. Use your sun resources to place plants on the lawn grid:
   - Click on a plant card at the top of the screen
   - Click on an empty grid cell to place the selected plant
4. Defend against zombies that approach from the right side
5. If zombies reach the left side of the lawn, lawn mowers will activate as a last defense
6. Survive three waves of zombies to win!

## Plants

1. **Sunflower (50 sun)**
   - Produces additional sun resources
   - No attack capabilities

2. **Peashooter (100 sun)**
   - Shoots peas at zombies
   - Medium damage

3. **Wall-nut (50 sun)**
   - Defensive plant with high health
   - No attack capabilities
   - Slows down zombie advance

## Requirements

- Python 3.x
- Pygame library

## Installation

If you don't have Pygame installed, you can install it with:

```
pip install pygame
```

## Game Features

- Resource management (sun collection)
- Strategic plant placement
- Multiple plant types with different abilities
- Increasing difficulty with each wave
- Last-defense lawn mowers
- Victory and game over conditions

## Assets

The game uses simple placeholder graphics generated with the included `generate_assets.py` script. Run this script to regenerate the assets if needed.
