from pyspark.sql import SparkSession
from pyspark.sql.functions import col, udf, count, when
from pyspark.sql.types import StringType, IntegerType, StructType, StructField
import os
import re

# Initialize Spark Session
spark = SparkSession.builder \
    .appName("Subnet Analysis") \
    .enableHiveSupport() \
    .getOrCreate()

# Function to parse subnet files from a local directory
def parse_local_subnet_files(data_dir):
    """
    Parse subnet files from a local directory and extract subnet, mask, and country code
    """
    subnet_data = []
    
    # Check if directory exists
    if not os.path.exists(data_dir):
        print(f"Directory {data_dir} does not exist")
        return subnet_data
    
    # Process each file in the directory
    for filename in os.listdir(data_dir):
        if filename.endswith(".txt"):
            country_code = filename.split(".")[0]
            file_path = os.path.join(data_dir, filename)
            
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#"):
                        # Extract subnet and mask
                        match = re.match(r"(\d+\.\d+\.\d+\.\d+)/(\d+)", line)
                        if match:
                            subnet, mask = match.groups()
                            subnet_data.append((subnet, mask, country_code))
            
            print(f"Processed {filename}")
    
    return subnet_data

# UDF to determine subnet class
def determine_subnet_class(subnet):
    """
    Determine if a subnet is Class A, B, or C
    Class A: ******* to *************** (First octet: 1-126)
    Class B: ********* to *************** (First octet: 128-191)
    Class C: ********* to *************** (First octet: 192-223)
    """
    try:
        first_octet = int(subnet.split('.')[0])
        if 1 <= first_octet <= 126:
            return "A"
        elif 128 <= first_octet <= 191:
            return "B"
        elif 192 <= first_octet <= 223:
            return "C"
        else:
            return "Other"
    except:
        return "Invalid"

# Main function
def main():
    # Specify the directory containing the subnet files
    data_dir = "country_subnets"  # Change this to your local directory path
    
    # Parse subnet files
    subnet_data = parse_local_subnet_files(data_dir)
    
    if not subnet_data:
        print("No subnet data found. Please check the data directory.")
        return
    
    # Create schema for DataFrame
    schema = StructType([
        StructField("subnet", StringType(), False),
        StructField("mask", StringType(), False),
        StructField("country_code", StringType(), False)
    ])
    
    # Create DataFrame
    subnet_df = spark.createDataFrame(subnet_data, schema)
    
    # Show sample data
    print("Sample subnet data:")
    subnet_df.show(5)
    
    # Register UDF
    subnet_class_udf = udf(determine_subnet_class, StringType())
    
    # Add subnet class column
    subnet_df = subnet_df.withColumn("subnet_class", subnet_class_udf(col("subnet")))
    
    # Count subnets by country and class
    result_df = subnet_df.groupBy("country_code") \
        .agg(
            count(when(col("subnet_class") == "A", 1)).alias("class_a_count"),
            count(when(col("subnet_class") == "B", 1)).alias("class_b_count"),
            count(when(col("subnet_class") == "C", 1)).alias("class_c_count"),
            count(when(col("subnet_class") == "Other", 1)).alias("other_count")
        )
    
    # Show results
    print("Subnet counts by country and class:")
    result_df.show()
    
    # Create Hive table and save results
    result_df.write.mode("overwrite").saveAsTable("subnet_counts_by_country")
    
    print("Results saved to Hive table: subnet_counts_by_country")
    
    # Clean up
    spark.stop()

if __name__ == "__main__":
    main()
